# Ultratrader Modular Optimization System

Sistema modular de alta performance para otimização de estratégias de trading de criptomoedas com gestão avançada de risco e backtesting abrangente.

## 🏗️ Arquitetura Modular Limpa

O sistema foi refatorado para uma arquitetura modular bem estruturada e otimizada:

```
ultratradernewversion/
├── core/                          # ⚙️ Componentes principais do sistema
│   ├── __init__.py               # Exportações do módulo core
│   ├── config.py                 # Configuração centralizada com validação
│   ├── data_manager.py           # Gestão de dados históricos e cache
│   └── backtest_engine.py        # Engine de backtest otimizado com Numba
├── utils/                         # 🛠️ Utilitários e ferramentas auxiliares
│   ├── __init__.py               # Exportações do módulo utils
│   ├── indicators.py             # Calculadora de indicadores técnicos
│   ├── scoring.py                # Sistema de scoring avançado (Quantum Score V3)
│   └── plotting.py               # Geração de gráficos e visualizações
├── risk/                          # 🛡️ Sistema de gestão de risco
│   ├── __init__.py               # Exportações do módulo risk
│   └── risk_manager.py           # Gestão de risco e dimensionamento de posições
├── optimization/                  # 🎯 Algoritmos de otimização
│   ├── __init__.py               # Exportações do módulo optimization
│   └── optimizer.py              # Otimização com Optuna e walk-forward
├── data_cache/                    # 💾 Cache de dados históricos (gerado automaticamente)
├── charts/                        # 📊 Gráficos gerados (gerado automaticamente)
├── validation_charts/             # 📈 Gráficos de validação (gerado automaticamente)
├── main.py                       # 🚀 Orquestrador principal modular
├── requirements.txt              # 📦 Dependências organizadas por categoria
├── optimized_strategies.json     # 🏆 Resultados das otimizações
├── ultratrader_modular.log       # 📝 Logs do sistema modular
├── optuna_studies.db             # 🗃️ Banco de dados dos estudos Optuna
└── env.txt                       # 🔐 Credenciais da API (configure suas chaves)
```

## 🚀 Principais Melhorias

### 1. **Modularização Completa**
- **Separação de responsabilidades**: Cada módulo tem uma função específica
- **Reutilização de código**: Componentes podem ser usados independentemente
- **Manutenibilidade**: Código organizado e fácil de manter
- **Testabilidade**: Módulos isolados facilitam testes unitários

### 2. **Configuração Centralizada**
```python
from core.config import config

# Configuração estruturada por categorias
config.market.timeframe = '15m'
config.trading.risk_per_trade = 0.008
config.optimization.optuna_trials_trend = 50
```

### 3. **Sistema de Gestão de Dados Robusto**
- Cache inteligente com expiração configurável
- Fetching de dados com retry automático e rate limiting
- Suporte a múltiplos timeframes simultâneos
- Processamento eficiente de grandes datasets

### 4. **Engine de Backtest Otimizado**
- Implementação com Numba para máxima performance
- Execução realística de ordens com slippage
- Gestão de risco integrada em tempo real
- Métricas abrangentes de performance

### 5. **Sistema de Scoring Avançado**
- **Quantum Score V3**: Scoring multi-dimensional aprimorado
- Walk-forward analysis para validação robusta
- Análise de robustez e estabilidade
- Scoring abrangente combinando múltiplas dimensões

### 6. **Gestão de Risco Profissional**
- Dimensionamento de posições com múltiplas metodologias
- Controle de drawdown em tempo real
- Circuit breakers e proteção de portfólio
- Ajuste dinâmico de risco baseado em performance

## 📦 Instalação

1. **Clone o repositório:**
```bash
git clone <repository-url>
cd ultratrader
```

2. **Instale as dependências:**
```bash
pip install -r requirements.txt
```

3. **Configure as credenciais da Binance:**
```bash
# Crie um arquivo .env na raiz do projeto
echo "BINANCE_API_KEY=sua_api_key" >> .env
echo "BINANCE_SECRET_KEY=sua_secret_key" >> .env
```

## 🏃‍♂️ Uso Rápido

### Execução Básica
```bash
python main.py
```

### Configuração Personalizada
```python
from core.config import config

# Personalizar configurações
config.market.target_symbol = 'ETHUSDT'
config.data.history_days = 120
config.trading.risk_per_trade = 0.01
```

### Uso Programático
```python
from main import UltraderOrchestrator
import asyncio

async def run_optimization():
    orchestrator = UltraderOrchestrator()
    await orchestrator.run_optimization_pipeline()

asyncio.run(run_optimization())
```

## 🔧 Componentes Principais

### Core Components

#### ConfiguraçÃo (`core/config.py`)
- Configuração estruturada em dataclasses
- Validação automática de parâmetros
- Suporte a variáveis de ambiente
- Configuração por categorias (market, data, trading, etc.)

#### Data Manager (`core/data_manager.py`)
- Fetching inteligente de dados históricos
- Sistema de cache com expiração
- Retry automático e rate limiting
- Suporte a múltiplos timeframes

#### Backtest Engine (`core/backtest_engine.py`)
- Performance otimizada com Numba
- Execução realística de ordens
- Gestão de risco integrada
- Métricas abrangentes

### Utilities

#### Indicadores Técnicos (`utils/indicators.py`)
- Ampla gama de indicadores técnicos
- Detecção de padrões de price action
- Indicadores de regime de mercado
- Análise multi-timeframe

#### Sistema de Scoring (`utils/scoring.py`)
- **Quantum Score V3**: Scoring multi-dimensional
- Walk-forward analysis
- Validação de estratégias
- Ranking e comparação

### Risk Management

#### Risk Manager (`risk/risk_manager.py`)
- Dimensionamento de posições avançado
- Controle de drawdown em tempo real
- Múltiplas metodologias de sizing
- Proteção de portfólio

### Optimization

#### Optimizer (`optimization/optimizer.py`)
- Otimização com Optuna
- Walk-forward analysis
- Pruning inteligente
- Multi-objective optimization

## 📊 Métricas e Scoring

### Quantum Score V3
O sistema utiliza uma versão aprimorada do Quantum Score que combina:

1. **Risk-Adjusted Return (40%)**: Retorno ajustado ao risco com Sortino ratio
2. **Consistency Factor (25%)**: Consistência baseada em profit factor e win rate
3. **Efficiency Multiplier (20%)**: Eficiência de trades e risk-reward ratio
4. **Risk Penalty (15%)**: Penalização por drawdown excessivo
5. **Stability Bonus (10%)**: Bonus por estratégias estáveis e conservadoras

### Walk-Forward Analysis
- Validação robusta out-of-sample
- Análise de estabilidade temporal
- Detecção de overfitting
- Métricas de degradação de performance

## 🎯 Configurações Principais

### Trading
```python
config.trading.risk_per_trade = 0.008          # 0.8% risco por trade
config.trading.max_drawdown_limit = 0.15       # 15% drawdown máximo
config.trading.max_consecutive_losses = 3      # Máximo 3 perdas consecutivas
config.trading.daily_loss_limit = 0.05         # 5% perda diária máxima
```

### Otimização
```python
config.optimization.optuna_trials_trend = 50       # Trials para estratégias de tendência
config.optimization.optuna_trials_reversion = 50   # Trials para reversão à média
config.optimization.min_sortino_oos = 0.2          # Sortino mínimo out-of-sample
config.optimization.min_profit_factor = 0.9        # Profit factor mínimo
```

### Market Filters
```python
config.market_filters.min_volatility_percentile = 10.0   # Volatilidade mínima
config.market_filters.max_volatility_percentile = 95.0   # Volatilidade máxima
config.market_filters.trend_strength_threshold = 0.3     # Força de tendência mínima
```

## 📈 Estratégias Suportadas

### 1. Trend Following
- Seguimento de tendência com EMAs
- Confirmação com MACD e RSI
- Filtros de volume e ADX
- Supertrend para direção

### 2. Mean Reversion
- Reversão com Bollinger Bands
- Osciladores (RSI, Stochastic, Williams %R)
- Money Flow Index (MFI)
- Commodity Channel Index (CCI)

### 3. Adaptive
- Combinação de trend following e mean reversion
- Detecção automática de regime de mercado
- Switching baseado em ADX e volatilidade
- Análise multi-timeframe

## 📊 Resultados e Outputs

### Arquivo de Resultados (`optimized_strategies.json`)
```json
{
  "BTCUSDT_Adaptive": {
    "rank": 1,
    "symbol": "BTCUSDT",
    "strategy_mode": "Adaptive",
    "parameters": { ... },
    "performance": {
      "comprehensive_score": 85.2,
      "quantum_score": 8.5,
      "sortino_ratio": 2.1,
      "total_return": 0.23,
      "max_drawdown": 0.08,
      "win_rate": 0.62,
      "trade_count": 45,
      "walk_forward_score": 1.8
    }
  }
}
```

### Logs Detalhados
- Log do sistema: `ultratrader_modular.log`
- Informações de otimização em tempo real
- Métricas de performance por janela
- Debugging e troubleshooting

## 🔍 Monitoramento e Debugging

### Logs Estruturados
```python
import logging
logger = logging.getLogger(__name__)

# Logs automáticos em cada componente
logger.info("Fetching market data for BTCUSDT")
logger.warning("Low volatility detected")
logger.error("Optimization failed for strategy")
```

### Métricas de Cache
```python
cache_info = data_manager.get_cache_info()
# {'file_count': 12, 'total_size_mb': 45.2, 'files': [...]}
```

## 🚀 Próximos Passos

### Implementações Futuras
1. **Interface Web**: Dashboard interativo com FastAPI
2. **Estratégias Específicas**: Módulos de estratégias especializadas
3. **Plotting Avançado**: Visualizações interativas com Plotly
4. **Database Integration**: Armazenamento de estratégias em banco
5. **API de Trading**: Execução automática de estratégias
6. **Machine Learning**: Integração com modelos de ML
7. **Multi-Exchange**: Suporte a múltiplas exchanges

### Melhorias de Performance
- Paralelização de otimizações
- Cache distribuído
- Processamento em GPU
- Otimização de memória

## 🛠️ Desenvolvimento

### Estrutura de Testes
```bash
pytest tests/                     # Executar todos os testes
pytest tests/test_backtest.py     # Testes específicos
pytest --cov=core tests/          # Cobertura de código
```

### Code Quality
```bash
black .                           # Formatação de código
flake8 .                         # Linting
mypy .                           # Type checking
```

### Contribuição
1. Fork o projeto
2. Crie uma branch para sua feature
3. Adicione testes para novas funcionalidades
4. Mantenha a cobertura de testes acima de 80%
5. Documente mudanças no README
6. Submeta um Pull Request

## 📄 Licença

Este projeto está licenciado sob a licença MIT. Veja o arquivo `LICENSE` para detalhes.

## ⚠️ Disclaimer

Este software é fornecido para fins educacionais e de pesquisa. Trading de criptomoedas envolve riscos significativos e pode resultar em perdas financeiras. Use por sua própria conta e risco.

## 📞 Suporte

Para suporte e questões:
- Abra uma issue no GitHub
- Consulte a documentação inline nos módulos
- Verifique os logs para debugging

---

**Ultratrader Modular** - Transformando estratégias de trading através de engenharia de software de qualidade. 🚀 