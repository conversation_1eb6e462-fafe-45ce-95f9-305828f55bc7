"""
Configuration module for Ultratrader optimization system.

Centralizes all configuration parameters with validation and environment support.
"""

import os
from pathlib import Path
from dataclasses import dataclass, field
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class MarketConfig:
    """Market and symbol configuration."""
    symbol_quote: str = 'USDT'
    timeframe: str = '15m'
    mta_timeframe: str = '1h'
    target_symbol: Optional[str] = None  # None for auto-selection of top symbols for diversification
    top_n_symbols: int = 5  # Increase to 5 symbols for better diversification and opportunities


@dataclass
class DataConfig:
    """Data fetching and caching configuration."""
    history_days: int = 180  # Expanded to 6 months for robust validation
    validation_days: int = 30  # Increased validation period
    cache_expiration_hours: int = 6
    max_concurrent_requests: int = 3
    
    # Cache and output directories
    cache_dir: Path = field(default_factory=lambda: Path("data_cache"))
    charts_dir: Path = field(default_factory=lambda: Path("charts"))
    validation_charts_dir: Path = field(default_factory=lambda: Path("validation_charts"))


@dataclass
class TradingConfig:
    """Trading and risk management configuration - AGGRESSIVE FOR 5%+ MONTHLY."""
    # AGGRESSIVE trading settings for higher returns
    risk_per_trade: float = 0.15  # 15% risk per trade (more aggressive)
    initial_balance: float = 1000.0
    futures_taker_fee: float = 0.0004  # Optimistic maker fees with volume discounts
    max_drawdown_limit: float = 0.60  # 60% maximum drawdown (more aggressive)

    # AGGRESSIVE risk controls for higher frequency trading
    max_consecutive_losses: int = 8  # Higher tolerance for more trades
    daily_loss_limit: float = 0.15  # 15% daily loss limit (more aggressive)
    min_win_rate: float = 0.15  # 15% minimum win rate (lower for higher frequency)
    max_position_size_pct: float = 0.25  # Max 25% of balance per trade (more aggressive)

    # NEW: Dynamic position sizing parameters
    volatility_position_scaling: bool = True  # Scale position size with volatility
    momentum_position_scaling: bool = True   # Scale position size with momentum
    max_leverage: float = 3.0                # Maximum leverage for futures
    min_trade_size_usd: float = 10.0         # Minimum trade size in USD


@dataclass
class OptimizationConfig:
    """Optimization algorithm configuration - ENHANCED FOR 5%+ MONTHLY RETURNS."""
    # INCREASED trials for better exploration of parameter space
    optuna_trials_trend: int = 100      # Increased for better optimization
    optuna_trials_reversion: int = 100  # Increased for better optimization
    optuna_trials_adx: int = 80         # Increased for better optimization
    optuna_trials_breakout: int = 100   # NEW: Breakout strategy trials
    optuna_trials_momentum: int = 100   # NEW: Momentum strategy trials

    # EXTENDED timeouts for thorough optimization
    optuna_timeout_per_phase: int = 600  # 10 minutes max per optimization
    optuna_timeout_per_trial: int = 60   # 60 seconds max per trial

    # AGGRESSIVE validation criteria for 5%+ monthly returns
    min_trades: int = 5              # Lower minimum for more strategies
    min_sortino_ratio: float = 0.2   # Much more lenient
    min_profit_factor: float = 0.8   # Allow some losing strategies if high return
    max_drawdown: float = 0.50       # 50% maximum (more aggressive)
    min_win_rate: float = 0.10       # Very low minimum (10%)
    min_walk_forward_windows: int = 1 # Allow single window validation
    stability_threshold: float = -0.5 # Allow negative stability if high returns
    consistency_threshold: float = 0.1 # Very low consistency requirement

    # RETURN-FOCUSED scoring weights
    quantum_score_weight: float = 0.3
    walk_forward_weight: float = 0.2
    robustness_weight: float = 0.1
    return_weight: float = 0.4        # NEW: Heavy weight on returns

    # --- AGGRESSIVE CRITERIA FOR 5%+ MONTHLY ---
    min_oos_return_monthly: float = 0.05  # 5% monthly target
    target_monthly_return: float = 0.08   # 8% monthly target (aggressive)
    oos_period_months: int = 1
    min_oos_return: float = 0.03      # 3% minimum OOS return (more lenient)
    min_sortino_oos: float = 0.1      # Very lenient OOS Sortino
    max_degradation: float = 0.6      # Allow significant degradation if high returns
    top_n: int = 10                   # More strategies to choose from

    # --- RETURN-FOCUSED SOTA WEIGHTS ---
    sota_weights: dict = None
    def __post_init__(self):
        if self.sota_weights is None:
            self.sota_weights = {
                'oos_return': 0.6,        # Heavy focus on returns
                'total_return': 0.2,      # Also consider in-sample returns
                'drawdown': -0.1,         # Light penalty for drawdown
                'sortino_oos': 0.05,      # Light weight on risk metrics
                'consistency': 0.05,      # Light weight on consistency
                'trades': 0.1             # Reward more trades
            }


@dataclass
class MarketFilterConfig:
    """Market condition filtering configuration."""
    # Volatility filters - aggressive for more opportunities
    min_volatility_percentile: float = 5.0   # Very low minimum - trade in almost all conditions  
    max_volatility_percentile: float = 98.0  # Very high maximum - trade in high volatility
    
    # Trend filters - very lenient for more trades
    trend_strength_threshold: float = 0.1    # Very low trend requirement
    adx_min_threshold: float = 10.0          # Very low ADX requirement
    adx_max_threshold: float = 80.0          # Allow very strong trends
    
    # Volume filters - aggressive for more opportunities
    volume_spike_threshold: float = 1.2      # 20% above average volume
    volume_drought_threshold: float = 0.6    # 40% below average volume (still allow)
    
    # Market timing filters - NEW aggressive approach
    market_session_enabled: bool = True      # Enable session-based filtering
    optimal_trading_hours: list = None       # UTC hours - will be set to [6,22] for Asian+European+US overlap
    avoid_weekend_gaps: bool = True          # Avoid trading near weekend close/open
    
    # Momentum filters - very aggressive
    momentum_lookback_periods: int = 24      # 6 hours lookback (24 x 15min bars)
    momentum_threshold: float = 0.002        # 0.2% minimum momentum to enter
    
    # Market regime detection - aggressive for trend-following
    regime_detection_enabled: bool = True    # Enable market regime detection
    trending_market_score_threshold: float = 0.3  # Low threshold for trending markets
    ranging_market_score_threshold: float = 0.7   # High threshold for ranging markets


@dataclass
class OutputConfig:
    """Output and scoring configuration."""
    final_scoring_method: str = 'Quantum'  # 'Quantum' or 'Sortino'
    max_strategies_to_save: int = 5


class Config:
    """Main configuration class that combines all config sections."""
    
    def __init__(self):
        self.market = MarketConfig()
        self.data = DataConfig()
        self.trading = TradingConfig()
        self.optimization = OptimizationConfig()
        self.market_filters = MarketFilterConfig()
        self.output = OutputConfig()
        
        # Initialize optimal trading hours for maximum market overlap
        if self.market_filters.optimal_trading_hours is None:
            self.market_filters.optimal_trading_hours = list(range(6, 22))  # 6 AM to 10 PM UTC
        
        # Ensure directories exist
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        self.data.cache_dir.mkdir(exist_ok=True)
        self.data.charts_dir.mkdir(exist_ok=True)
        self.data.validation_charts_dir.mkdir(exist_ok=True)
    
    def get_binance_credentials(self) -> tuple[str, str]:
        """Get Binance API credentials from environment variables."""
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_SECRET_KEY')
        
        if not api_key or not api_secret:
            raise ValueError(
                "Binance API credentials not found. "
                "Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables."
            )
        
        return api_key, api_secret
    
    def validate(self):
        """Validate configuration parameters."""
        # Market validation
        if self.market.timeframe not in ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d']:
            raise ValueError(f"Invalid timeframe: {self.market.timeframe}")
        
        # Trading validation - Updated for aggressive parameters
        if not 0 < self.trading.risk_per_trade <= 0.15:  # Allow up to 15% risk for aggressive strategies
            raise ValueError("Risk per trade must be between 0 and 15%")
        
        if not 0 < self.trading.max_drawdown_limit <= 1.0:
            raise ValueError("Max drawdown limit must be between 0 and 100%")
        
        # Data validation
        if self.data.history_days < self.data.validation_days:
            raise ValueError("History days must be greater than validation days")
            
        # Optimization validation - ensure minimum requirements
        if self.optimization.min_trades < 1:
            raise ValueError("Minimum trades must be at least 1")
            
        if self.optimization.optuna_trials_trend < 5:
            raise ValueError("Optuna trials must be at least 5 for meaningful optimization")


# Global configuration instance
config = Config()


def update_config_from_env():
    """Update configuration from environment variables if available."""
    # Market settings
    if symbol := os.getenv('TARGET_SYMBOL'):
        config.market.target_symbol = symbol
    
    if timeframe := os.getenv('TIMEFRAME'):
        config.market.timeframe = timeframe
    
    # Trading settings
    if risk := os.getenv('RISK_PER_TRADE'):
        config.trading.risk_per_trade = float(risk)
    
    if balance := os.getenv('INITIAL_BALANCE'):
        config.trading.initial_balance = float(balance)
    
    # Data settings
    if days := os.getenv('HISTORY_DAYS'):
        config.data.history_days = int(days)


# Auto-update from environment on import
update_config_from_env()
config.validate() 