"""
Optimization engine for Ultratrader optimization system.

Advanced optimization using Optuna with walk-forward analysis,
multi-objective optimization, and parameter space exploration.
"""

import optuna
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Callable, Tuple
import logging

from core.backtest_engine import BacktestEngine
from utils.indicators import IndicatorCalculator
from utils.scoring import ScoringSystem, PerformanceMetrics


# Suppress Optuna logs completely during optimization
optuna.logging.set_verbosity(optuna.logging.ERROR)


class OptunaOptimizer:
    """
    Advanced Optuna-based optimizer with strategy-specific parameter spaces.
    
    Features:
    - Strategy-specific parameter optimization
    - Multi-objective optimization
    - Pruning for faster convergence
    - Parallel optimization
    - Custom scoring objectives
    """
    
    def __init__(self, backtest_engine: Optional[BacktestEngine] = None):
        self.backtest_engine = backtest_engine or BacktestEngine()
        self.study = None
        self.best_params = None
        self.optimization_history = []
        self.progress_counter = 0
        self.total_trials = 0

    def _progress_callback(self, study, trial):
        """Progress callback for optimization tracking."""
        self.progress_counter += 1
        if self.progress_counter % 5 == 0:  # Show progress every 5 trials
            best_value = study.best_value if study.best_value is not None else 0
            print(f"      Trial {self.progress_counter}/{self.total_trials}: Best score = {best_value:.2f}")

    def _reset_progress(self, n_trials):
        """Reset progress tracking."""
        self.progress_counter = 0
        self.total_trials = n_trials
    
    def optimize_strategy(
        self,
        df: pd.DataFrame,
        mta_df: pd.DataFrame,
        strategy_mode: str,
        n_trials: int,
        timeout: Optional[int] = None,
        scoring_method: str = 'quantum'
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Optimize strategy parameters using Optuna.
        
        Args:
            df: Main timeframe data with indicators
            mta_df: Multi-timeframe analysis data
            strategy_mode: Strategy mode ('Trend', 'Reversion', 'Adaptive')
            n_trials: Number of optimization trials
            timeout: Timeout in seconds
            scoring_method: Scoring method ('quantum', 'sortino', 'comprehensive')
            
        Returns:
            Tuple of (best_params, optimization_results)
        """
        
        # Create study with optimized sampler for faster performance
        sampler = optuna.samplers.TPESampler(
            n_startup_trials=min(10, n_trials // 4),  # Further reduced startup trials
            n_ei_candidates=15,  # Reduced candidates for faster sampling
            seed=42,
            multivariate=True,  # Enable multivariate sampling for better parameter correlation
            constant_liar=True  # Enable constant liar for parallel optimization
        )

        # Aggressive pruning for much faster optimization
        pruner = optuna.pruners.MedianPruner(
            n_startup_trials=min(5, n_trials // 8),  # Very aggressive pruning
            n_warmup_steps=10,  # Reduced warmup steps
            interval_steps=3  # Check pruning more frequently
        )
        
        self.study = optuna.create_study(
            direction='maximize',
            sampler=sampler,
            pruner=pruner
        )
        
        # Create objective function
        objective_func = self._create_objective_function(
            df, mta_df, strategy_mode, scoring_method
        )
        
        # Run optimization with progress tracking
        self._reset_progress(n_trials)
        try:
            self.study.optimize(
                objective_func,
                n_trials=n_trials,
                timeout=timeout,
                show_progress_bar=False,
                callbacks=[self._progress_callback]
            )
        except Exception as e:
            logging.warning(f"Optimization interrupted: {e}")
            if len(self.study.trials) == 0:
                raise
        
        self.best_params = self.study.best_params
        
        # Calculate final metrics with best parameters
        final_metrics = self._calculate_final_metrics(
            df, mta_df, strategy_mode, self.best_params
        )
        
        optimization_results = {
            'best_score': self.study.best_value,
            'n_trials': len(self.study.trials),
            'best_trial': self.study.best_trial.number,
            'optimization_history': [(t.number, t.value) for t in self.study.trials if t.value is not None],
            'final_metrics': final_metrics
        }
        
        return self.best_params, optimization_results
    
    def _create_objective_function(
        self,
        df: pd.DataFrame,
        mta_df: pd.DataFrame,
        strategy_mode: str,
        scoring_method: str
    ) -> Callable:
        """Create Optuna objective function."""
        
        def objective(trial):
            try:
                # Sample parameters based on strategy mode
                params = self._sample_parameters(trial, strategy_mode)
                
                # Validate parameters before proceeding
                if not self._validate_parameters(params):
                    raise ValueError(f"Invalid parameters sampled: {params}")
                
                # Calculate indicators with sampled parameters
                df_with_indicators = IndicatorCalculator.calculate_all_indicators(df.copy(), params)
                
                # Validate indicators were calculated properly
                if df_with_indicators.empty or len(df_with_indicators) < 50:
                    raise ValueError(f"Insufficient data after indicator calculation: {len(df_with_indicators)} bars")
                
                # Check for excessive NaN values
                nan_ratio = df_with_indicators.isnull().sum().sum() / (len(df_with_indicators) * len(df_with_indicators.columns))
                if nan_ratio > 0.3:  # More than 30% NaN values
                    raise ValueError(f"Too many NaN values in indicators: {nan_ratio:.2%}")
                
                # Add MTA trend
                df_with_indicators = IndicatorCalculator.add_mta_trend(df_with_indicators, mta_df)
                
                # Final data validation
                df_with_indicators = df_with_indicators.dropna()
                if len(df_with_indicators) < 30:  # Minimum viable dataset
                    raise ValueError(f"Insufficient clean data for backtest: {len(df_with_indicators)} bars")
                
                # Run backtest with timeout protection
                try:
                    results = self.backtest_engine.run_backtest(
                        df_with_indicators, params, strategy_mode
                    )
                except Exception as backtest_error:
                    raise ValueError(f"Backtest execution failed: {str(backtest_error)}")
                
                # Validate backtest results
                if not hasattr(results, 'trade_count') or results.trade_count is None:
                    raise ValueError("Invalid backtest results: missing trade_count")
                
                # Calculate score based on method
                try:
                    if scoring_method == 'quantum':
                        score, _ = ScoringSystem.calculate_quantum_score_v3(
                            results.sortino_ratio,
                            results.profit_factor,
                            results.max_drawdown,
                            results.total_return,
                            results.win_rate,
                            results.trade_count,
                            results.avg_win,
                            results.avg_loss
                        )
                    elif scoring_method == 'sortino':
                        score = results.sortino_ratio if hasattr(results, 'sortino_ratio') else -999.0
                    elif scoring_method == 'comprehensive':
                        score, _ = ScoringSystem.calculate_quantum_score_v3(
                            results.sortino_ratio,
                            results.profit_factor,
                            results.max_drawdown,
                            results.total_return,
                            results.win_rate,
                            results.trade_count,
                            results.avg_win,
                            results.avg_loss
                        )
                    else:
                        score = results.sortino_ratio if hasattr(results, 'sortino_ratio') else -999.0
                except Exception as scoring_error:
                    raise ValueError(f"Scoring calculation failed: {str(scoring_error)}")
                
                # Validate score
                if score is None or np.isnan(score) or np.isinf(score):
                    raise ValueError(f"Invalid score calculated: {score}")
                
                # More lenient pruning for initial optimization
                if results.trade_count < 1:  # At least 1 trade
                    raise optuna.TrialPruned("No trades generated")
                
                if results.max_drawdown > 0.50:  # 50% max drawdown (very lenient)
                    raise optuna.TrialPruned(f"Excessive drawdown: {results.max_drawdown:.2%}")
                
                return float(score)
                
            except optuna.TrialPruned as pruned_error:
                # Re-raise pruned trials (expected behavior)
                raise pruned_error
            except Exception as e:
                # Simplified error logging - only log critical errors
                if trial.number % 10 == 0:  # Only log every 10th error to reduce spam
                    logging.warning(f"Trial {trial.number} failed: {type(e).__name__}")
                return -999.0
        
        return objective
    
    def _sample_parameters(self, trial, strategy_mode: str) -> Dict[str, Any]:
        """Sample parameters based on strategy mode."""
        
        # Base parameters for all strategies - optimized for higher profits
        # Smart EMA sampling to ensure ema_fast < ema_slow always
        ema_fast = trial.suggest_int('ema_fast', 5, 30)
        ema_slow = trial.suggest_int('ema_slow', max(ema_fast + 1, 20), 100)  # Ensure slow > fast
        
        params = {
            # Core indicators - properly ordered EMAs
            'ema_fast': ema_fast,
            'ema_slow': ema_slow,
            'atr_len': trial.suggest_int('atr_len', 8, 25),
            'vol_sma_len': trial.suggest_int('vol_sma_len', 8, 40),
            
            # Parâmetros de risk management e controle de frequência - sempre ativos
            'sl_atr_mult': trial.suggest_float('sl_atr_mult', 2.0, 8.0),  # Stops mais curtos
            'trail_atr_mult': trial.suggest_float('trail_atr_mult', 1.0, 6.0),  # Trailing mais curto
            'risk_multiplier': trial.suggest_float('risk_multiplier', 1.0, 6.0),  # Permitir risco maior
            'min_bars_between_trades': trial.suggest_int('min_bars_between_trades', 1, 5),  # Muito mais trades
            # Take profit parameter - mais agressivo
            'tp_atr_mult': trial.suggest_float('tp_atr_mult', 1.5, 4.0)  # TP mais curto
        }
        
        # Strategy-specific parameters - optimized for better entry/exit
        if strategy_mode in ['Trend', 'Adaptive']:
            # Smart MACD sampling to ensure macd_fast < macd_slow
            macd_fast = trial.suggest_int('macd_fast', 6, 20)
            macd_slow = trial.suggest_int('macd_slow', max(macd_fast + 1, 18), 50)  # Ensure slow > fast
            
            params.update({
                'st_len': trial.suggest_int('st_len', 5, 40),  # Wider range for Supertrend
                'st_mult': trial.suggest_float('st_mult', 1.5, 5.0),  # More aggressive multiplier
                'adx_len': trial.suggest_int('adx_len', 8, 25),
                'adx_threshold': trial.suggest_float('adx_threshold', 15, 40),  # Wider ADX threshold
                'trend_confirmations': trial.suggest_int('trend_confirmations', 1, 2),  # Less confirmations needed
                
                # MACD parameters - properly ordered
                'macd_fast': macd_fast,
                'macd_slow': macd_slow,
                'macd_signal': trial.suggest_int('macd_signal', 5, 15),
                
                # RSI parameters - more flexible
                'rsi_len': trial.suggest_int('rsi_len', 8, 25),
            })
        
        if strategy_mode in ['Reversion', 'Adaptive']:
            params.update({
                # Bollinger Bands - more sensitive for more opportunities
                'bb_len': trial.suggest_int('bb_len', 10, 40),
                'bb_std': trial.suggest_float('bb_std', 1.2, 3.5),
                'bb_oversold': trial.suggest_float('bb_oversold', 0.02, 0.25),  # More sensitive
                'bb_overbought': trial.suggest_float('bb_overbought', 0.75, 0.98),  # More sensitive
                
                # MFI and RSI - more aggressive ranges
                'mfi_len': trial.suggest_int('mfi_len', 8, 30),
                'rsi_len': trial.suggest_int('rsi_len', 8, 25),
                'rsi_oversold': trial.suggest_float('rsi_oversold', 15, 40),  # More sensitive
                'rsi_overbought': trial.suggest_float('rsi_overbought', 60, 85),  # More sensitive
                
                # Stochastic parameters - wider ranges
                'stoch_k': trial.suggest_int('stoch_k', 8, 25),
                'stoch_d': trial.suggest_int('stoch_d', 3, 10),
                
                # Williams %R - more sensitive
                'willr_len': trial.suggest_int('willr_len', 8, 30),
                
                # CCI - wider range
                'cci_len': trial.suggest_int('cci_len', 10, 30),
            })
        
        if strategy_mode == 'Ensemble':
            # Ensemble strategy combines all indicators from both trend and reversion
            # Smart MACD sampling to ensure macd_fast < macd_slow
            macd_fast = trial.suggest_int('macd_fast', 6, 20)
            macd_slow = trial.suggest_int('macd_slow', max(macd_fast + 1, 18), 50)
            
            params.update({
                # Trend indicators
                'st_len': trial.suggest_int('st_len', 5, 40),
                'st_mult': trial.suggest_float('st_mult', 1.5, 5.0),
                'adx_len': trial.suggest_int('adx_len', 8, 25),
                'adx_threshold': trial.suggest_float('adx_threshold', 15, 40),
                'trend_confirmations': trial.suggest_int('trend_confirmations', 1, 2),
                'macd_fast': macd_fast,
                'macd_slow': macd_slow,
                'macd_signal': trial.suggest_int('macd_signal', 5, 15),
                
                # Reversion indicators
                'bb_len': trial.suggest_int('bb_len', 10, 40),
                'bb_std': trial.suggest_float('bb_std', 1.2, 3.5),
                'bb_oversold': trial.suggest_float('bb_oversold', 0.02, 0.25),
                'bb_overbought': trial.suggest_float('bb_overbought', 0.75, 0.98),
                'mfi_len': trial.suggest_int('mfi_len', 8, 30),
                'rsi_len': trial.suggest_int('rsi_len', 8, 25),
                'rsi_oversold': trial.suggest_float('rsi_oversold', 15, 40),
                'rsi_overbought': trial.suggest_float('rsi_overbought', 60, 85),
                'stoch_k': trial.suggest_int('stoch_k', 8, 25),
                'stoch_d': trial.suggest_int('stoch_d', 3, 10),
                'willr_len': trial.suggest_int('willr_len', 8, 30),
                'cci_len': trial.suggest_int('cci_len', 10, 30),
                
                # Ensemble-specific parameters
                'ensemble_threshold': trial.suggest_float('ensemble_threshold', 0.3, 0.6),  # Lower consensus threshold
                'volatility_sensitivity': trial.suggest_float('volatility_sensitivity', 0.5, 2.0),  # Volatility response
                'min_volatility_threshold': trial.suggest_float('min_volatility_threshold', 0.0001, 0.002),  # Much lower vol filter
                'volume_threshold': trial.suggest_float('volume_threshold', 1.0, 1.3),  # Less restrictive volume filter
            })
        
        return params
    
    def _validate_parameters(self, params: Dict[str, Any]) -> bool:
        """Validate that sampled parameters are reasonable and won't cause errors."""
        try:
            # Simplified validation since smart sampling now ensures valid combinations
            
            # Basic positive value checks only
            risk_mult = params.get('risk_multiplier', 1.0)
            if risk_mult <= 0 or risk_mult > 20:  # Allow up to 20x risk multiplier for aggressive strategies
                return False
            
            # Take profit validation - must be positive
            tp_mult = params.get('tp_atr_mult', 3.0)
            if tp_mult <= 0:
                return False
            
            # Stop loss validation - must be positive  
            sl_mult = params.get('sl_atr_mult', 2.0)
            if sl_mult <= 0:
                return False
            
            # Trail stop validation - if present, must be positive
            trail_mult = params.get('trail_atr_mult', 1.0)
            if 'trail_atr_mult' in params and trail_mult <= 0:
                return False
                
            return True
            
        except Exception:
            # If validation itself fails, be permissive
            return True
    
    def _calculate_final_metrics(
        self,
        df: pd.DataFrame,
        mta_df: pd.DataFrame,
        strategy_mode: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate final metrics with best parameters."""
        
        df_with_indicators = IndicatorCalculator.calculate_all_indicators(df.copy(), params)
        df_with_indicators = IndicatorCalculator.add_mta_trend(df_with_indicators, mta_df)
        
        results = self.backtest_engine.run_backtest(
            df_with_indicators, params, strategy_mode
        )
        
        quantum_score, quantum_components = ScoringSystem.calculate_quantum_score_v3(
            results.sortino_ratio,
            results.profit_factor,
            results.max_drawdown,
            results.total_return,
            results.win_rate,
            results.trade_count,
            results.avg_win,
            results.avg_loss
        )
        
        return {
            'sortino_ratio': results.sortino_ratio,
            'profit_factor': results.profit_factor,
            'max_drawdown': results.max_drawdown,
            'total_return': results.total_return,
            'win_rate': results.win_rate,
            'trade_count': results.trade_count,
            'quantum_score': quantum_score,
            'quantum_components': quantum_components,
            'sharpe_ratio': results.sharpe_ratio,
            'calmar_ratio': results.calmar_ratio
        }


class WalkForwardAnalyzer:
    """
    Walk-forward analysis for robust strategy validation.
    
    Features:
    - Rolling window optimization
    - Out-of-sample validation
    - Stability analysis
    - Performance degradation detection
    """
    
    def __init__(self, optimizer: Optional[OptunaOptimizer] = None):
        self.optimizer = optimizer or OptunaOptimizer()
        self.walk_forward_results = []
    
    def run_walk_forward_analysis(
        self,
        df: pd.DataFrame,
        mta_df: pd.DataFrame,
        strategy_mode: str,
        in_sample_days: int,
        out_of_sample_days: int,
        step_days: int,
        n_trials_per_window: int = 30
    ) -> Dict[str, Any]:
        """
        Run walk-forward analysis on the dataset.
        
        Args:
            df: Main timeframe data
            mta_df: Multi-timeframe analysis data
            strategy_mode: Strategy mode
            in_sample_days: Days for in-sample optimization
            out_of_sample_days: Days for out-of-sample validation
            step_days: Days to step forward each iteration
            n_trials_per_window: Optuna trials per window
            
        Returns:
            Walk-forward analysis results
        """
        
        self.walk_forward_results.clear()
        total_bars = len(df)
        total_days = total_bars / (24 * 4)  # Assuming 15min bars
        window_count = 0
        
        print(f"Walk-forward analysis: {total_bars} bars ({total_days:.1f} days)")
        print(f"Parameters: IS={in_sample_days}d, OOS={out_of_sample_days}d, step={step_days}d")
        
        # Convert days to bars (15min timeframe)
        is_bars = in_sample_days * 24 * 4
        oos_bars = out_of_sample_days * 24 * 4
        step_bars = step_days * 24 * 4
        
        # Calculate maximum possible windows
        min_window_size = is_bars + oos_bars
        if total_bars < min_window_size:
            print(f"Error: Insufficient data. Need {min_window_size} bars, have {total_bars}")
            return self._aggregate_walk_forward_results()
        
        max_windows = max(1, int((total_bars - min_window_size) / step_bars) + 1)
        print(f"Maximum possible windows: {max_windows}")
        
        successful_windows = 0
        failed_windows = 0
        
        for window_idx in range(max_windows):
            start_bar = window_idx * step_bars
            
            # Define in-sample and out-of-sample periods
            is_start = int(start_bar)
            is_end = int(start_bar + is_bars)
            oos_start = is_end
            oos_end = int(oos_start + oos_bars)
            
            # Check bounds
            if oos_end > total_bars:
                print(f"Window {window_idx+1}: Reached end of data at bar {oos_end}")
                break
            
            # Extract data windows
            is_df = df.iloc[is_start:is_end].copy()
            oos_df = df.iloc[oos_start:oos_end].copy()
            
            # Corresponding MTA data if available
            is_mta_df = mta_df.iloc[is_start:is_end].copy() if not mta_df.empty else pd.DataFrame()
            oos_mta_df = mta_df.iloc[oos_start:oos_end].copy() if not mta_df.empty else pd.DataFrame()
            
            # More aggressive minimum data requirements for maximum opportunity
            min_is_bars = max(30, is_bars // 8)  # At least 30 bars or 12.5% of intended
            min_oos_bars = max(10, oos_bars // 8)  # At least 10 bars or 12.5% of intended
            
            if len(is_df) < min_is_bars or len(oos_df) < min_oos_bars:
                print(f"Window {window_idx+1}: Insufficient data (IS: {len(is_df)}, OOS: {len(oos_df)})")
                failed_windows += 1
                continue
            
            try:
                print(f"Processing window {window_idx+1}/{max_windows}...")
                
                # Optimize on in-sample data with reduced timeout for walk-forward
                timeout = max(45, min(90, n_trials_per_window * 4))  # More conservative timeout for anti-overfitting
                
                best_params, opt_results = self.optimizer.optimize_strategy(
                    is_df, is_mta_df, strategy_mode, n_trials_per_window, timeout=timeout
                )
                
                if not best_params or not opt_results:
                    print(f"Window {window_idx+1}: Optimization failed")
                    failed_windows += 1
                    continue
                
                # Test on out-of-sample data
                oos_df_with_indicators = IndicatorCalculator.calculate_all_indicators(oos_df, best_params)
                oos_df_with_indicators = IndicatorCalculator.add_mta_trend(oos_df_with_indicators, oos_mta_df)
                
                # Handle potential NaN values
                oos_df_with_indicators = oos_df_with_indicators.ffill()
                oos_df_with_indicators = oos_df_with_indicators.dropna()
                
                if len(oos_df_with_indicators) < 10:  # Minimum bars for meaningful test
                    print(f"Window {window_idx+1}: Insufficient OOS data after indicator calculation")
                    failed_windows += 1
                    continue
                
                oos_results = self.optimizer.backtest_engine.run_backtest(
                    oos_df_with_indicators, best_params, strategy_mode
                )
                
                # Validate results
                if not hasattr(oos_results, 'sortino_ratio'):
                    print(f"Window {window_idx+1}: Invalid backtest results")
                    failed_windows += 1
                    continue
                
                # Store results
                window_result = {
                    'window': window_count,
                    'is_start_date': df.index[is_start],
                    'is_end_date': df.index[is_end-1],
                    'oos_start_date': df.index[oos_start],
                    'oos_end_date': df.index[oos_end-1],
                    'best_params': best_params,
                    'is_metrics': opt_results['final_metrics'],
                    'oos_metrics': {
                        'sortino_ratio': float(oos_results.sortino_ratio),
                        'profit_factor': float(oos_results.profit_factor),
                        'max_drawdown': float(oos_results.max_drawdown),
                        'total_return': float(oos_results.total_return),
                        'win_rate': float(oos_results.win_rate),
                        'trade_count': int(oos_results.trade_count),
                        'avg_win': float(getattr(oos_results, 'avg_win', 0)),
                        'avg_loss': float(getattr(oos_results, 'avg_loss', 0))
                    }
                }
                
                self.walk_forward_results.append(window_result)
                window_count += 1
                successful_windows += 1
                
                is_sortino = opt_results['final_metrics']['sortino_ratio']
                oos_sortino = oos_results.sortino_ratio
                print(f"Window {window_idx+1}: IS Sortino={is_sortino:.2f}, "
                      f"OOS Sortino={oos_sortino:.2f}, Trades={oos_results.trade_count}")
                
            except Exception as e:
                print(f"Error in window {window_idx+1}: {str(e)}")
                failed_windows += 1
                continue
        
        print(f"Walk-forward completed: {successful_windows} successful, {failed_windows} failed windows")
        
        # Aggregate results
        return self._aggregate_walk_forward_results()
    
    def _aggregate_walk_forward_results(self) -> Dict[str, Any]:
        """Aggregate walk-forward results with enhanced error handling."""
        
        if not self.walk_forward_results:
            print("No valid walk-forward windows found")
            return {
                'valid_windows': 0,
                'aggregate_metrics': {},
                'stability_analysis': {},
                'walk_forward_score': 0.0,
                'individual_windows': []
            }
        
        num_windows = len(self.walk_forward_results)
        print(f"Aggregating results from {num_windows} valid windows")
        
        try:
            # Extract metrics with error handling
            is_sortino = []
            oos_sortino = []
            is_returns = []
            oos_returns = []
            
            for r in self.walk_forward_results:
                # Safely extract in-sample metrics
                is_metrics = r.get('is_metrics', {})
                is_sortino.append(float(is_metrics.get('sortino_ratio', 0.0)))
                is_returns.append(float(is_metrics.get('total_return', 0.0)))
                
                # Safely extract out-of-sample metrics
                oos_metrics = r.get('oos_metrics', {})
                oos_sortino.append(float(oos_metrics.get('sortino_ratio', 0.0)))
                oos_returns.append(float(oos_metrics.get('total_return', 0.0)))
            
            # Calculate stability metrics with safety checks
            mean_is_sortino = np.mean(is_sortino) if is_sortino else 0.0
            mean_oos_sortino = np.mean(oos_sortino) if oos_sortino else 0.0
            mean_is_returns = np.mean(is_returns) if is_returns else 0.0
            mean_oos_returns = np.mean(oos_returns) if oos_returns else 0.0
            
            sortino_stability = (mean_oos_sortino / mean_is_sortino) if mean_is_sortino > 0 else 0.0
            return_stability = (mean_oos_returns / mean_is_returns) if mean_is_returns != 0 else 0.0
            
            # Calculate cumulative return more safely
            try:
                if oos_returns:
                    # Convert to multiplicative returns for compounding
                    multiplicative_returns = [1 + max(r, -0.99) for r in oos_returns]  # Prevent -100% returns
                    cumulative_oos_return = np.prod(multiplicative_returns) - 1
                else:
                    cumulative_oos_return = 0.0
            except (OverflowError, ValueError):
                cumulative_oos_return = np.sum(oos_returns)  # Fallback to additive
            
            # Create aggregate performance metrics
            avg_metrics = {}
            for metric in ['profit_factor', 'max_drawdown', 'win_rate', 'trade_count', 'avg_win', 'avg_loss']:
                values = []
                for r in self.walk_forward_results:
                    is_val = r.get('is_metrics', {}).get(metric, 0.0)
                    oos_val = r.get('oos_metrics', {}).get(metric, 0.0)
                    values.extend([is_val, oos_val])
                
                avg_metrics[f'avg_{metric}'] = np.mean(values) if values else 0.0
            
            # Calculate walk-forward score using ScoringSystem
            is_metrics = PerformanceMetrics(
                sortino_ratio=mean_is_sortino,
                profit_factor=avg_metrics.get('avg_profit_factor', 1.0),
                max_drawdown=avg_metrics.get('avg_max_drawdown', 0.0),
                total_return=mean_is_returns,
                win_rate=avg_metrics.get('avg_win_rate', 0.0),
                trade_count=int(avg_metrics.get('avg_trade_count', 0)),
                avg_win=avg_metrics.get('avg_avg_win', 0.0),
                avg_loss=avg_metrics.get('avg_avg_loss', 0.0),
                sharpe_ratio=0, calmar_ratio=0, recovery_factor=0, var_95=0, expected_shortfall=0
            )
            
            oos_metrics = PerformanceMetrics(
                sortino_ratio=mean_oos_sortino,
                profit_factor=avg_metrics.get('avg_profit_factor', 1.0),
                max_drawdown=avg_metrics.get('avg_max_drawdown', 0.0),
                total_return=cumulative_oos_return,
                win_rate=avg_metrics.get('avg_win_rate', 0.0),
                trade_count=int(np.sum([r.get('oos_metrics', {}).get('trade_count', 0) for r in self.walk_forward_results])),
                avg_win=avg_metrics.get('avg_avg_win', 0.0),
                avg_loss=avg_metrics.get('avg_avg_loss', 0.0),
                sharpe_ratio=0, calmar_ratio=0, recovery_factor=0, var_95=0, expected_shortfall=0
            )
            
            walk_forward_score, detailed_metrics = ScoringSystem.calculate_walk_forward_score(
                is_metrics, oos_metrics
            )
            
            aggregate_metrics = {
                'cumulative_oos_return': cumulative_oos_return,
                'avg_oos_sortino': mean_oos_sortino,
                'sortino_stability': sortino_stability,
                'return_stability': return_stability,
                'avg_is_sortino': mean_is_sortino,
                'avg_is_return': mean_is_returns,
                'total_oos_trades': oos_metrics.trade_count
            }
            
            print(f"Walk-forward aggregation complete:")
            print(f"  - Avg OOS Sortino: {mean_oos_sortino:.3f}")
            print(f"  - Cumulative OOS Return: {cumulative_oos_return:.3f}")
            print(f"  - Sortino Stability: {sortino_stability:.3f}")
            print(f"  - Walk-Forward Score: {walk_forward_score:.3f}")
            
            return {
                'valid_windows': num_windows,
                'aggregate_metrics': aggregate_metrics,
                'stability_analysis': detailed_metrics,
                'walk_forward_score': walk_forward_score,
                'individual_windows': self.walk_forward_results
            }
            
        except Exception as e:
            print(f"Error in walk-forward aggregation: {str(e)}")
            # Return minimal valid result
            return {
                'valid_windows': num_windows,
                'aggregate_metrics': {
                    'cumulative_oos_return': 0.0,
                    'avg_oos_sortino': 0.0,
                    'sortino_stability': 0.0,
                    'return_stability': 0.0
                },
                'stability_analysis': {},
                'walk_forward_score': 0.0,
                'individual_windows': self.walk_forward_results
            } 