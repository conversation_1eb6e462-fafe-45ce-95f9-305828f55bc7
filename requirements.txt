# Core Dependencies
pandas>=2.0.0
numpy>=1.24.0
python-dotenv>=1.0.0

# Trading and Financial Data
python-binance>=1.0.16
pandas-ta>=0.3.14b0

# Optimization and Machine Learning
optuna>=3.1.0
scikit-learn>=1.3.0

# Performance Optimization
numba>=0.57.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# Data Processing and Storage
feather-format>=0.4.1
pyarrow>=12.0.0

# Async Programming
aiohttp>=3.8.0
asyncio-throttle>=1.0.2

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0

# Logging and Monitoring
coloredlogs>=15.0.1
tqdm>=4.65.0

# Mathematical and Statistical
scipy>=1.10.0
statsmodels>=0.14.0

# Database (Optional - for strategy storage)
sqlalchemy>=2.0.0
sqlite3

# Web Framework (Optional - for web interface)
fastapi>=0.100.0
uvicorn>=0.22.0

# Configuration Management
pydantic>=2.0.0
pyyaml>=6.0

# Utilities
click>=8.1.0
rich>=13.4.0 