"""
Technical indicators module for Ultratrader optimization system.

Provides comprehensive technical indicator calculations with error handling
and performance optimizations.
"""

import pandas as pd
import pandas_ta
import numpy as np
from typing import Dict, Any
import warnings

# Suppress pandas warnings for indicator calculations
warnings.filterwarnings("ignore", message="Setting an item of incompatible dtype is deprecated*", 
                       category=FutureWarning)


class IndicatorCalculator:
    """
    Calculates technical indicators with robust error handling and performance optimization.

    Features:
    - Comprehensive set of technical indicators
    - Error handling for missing data
    - Performance optimizations with caching
    - Market regime detection
    - Volume analysis
    - Price action patterns
    """

    # Class-level cache for indicator calculations
    _indicator_cache = {}
    _cache_stats = {'hits': 0, 'misses': 0}

    @classmethod
    def clear_cache(cls):
        """Clear the indicator cache to free memory."""
        cls._indicator_cache.clear()
        cls._cache_stats = {'hits': 0, 'misses': 0}

    @classmethod
    def get_cache_stats(cls):
        """Get cache performance statistics."""
        total = cls._cache_stats['hits'] + cls._cache_stats['misses']
        hit_rate = cls._cache_stats['hits'] / total if total > 0 else 0
        return {
            'hits': cls._cache_stats['hits'],
            'misses': cls._cache_stats['misses'],
            'hit_rate': hit_rate,
            'cache_size': len(cls._indicator_cache)
        }
    
    @classmethod
    def calculate_all_indicators(cls, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """
        Calculate all technical indicators for the given dataframe.
        
        Args:
            df: OHLCV dataframe
            params: Parameters for indicator calculation
            
        Returns:
            DataFrame with all indicators added
        """
        # Create cache key based on data hash and parameters
        data_hash = hash(tuple(df.iloc[-1].values)) if not df.empty else 0
        param_hash = hash(frozenset(params.items()))
        cache_key = f"{data_hash}_{param_hash}_{len(df)}"

        # Check cache first
        if cache_key in cls._indicator_cache:
            cls._cache_stats['hits'] += 1
            cached_result = cls._indicator_cache[cache_key]
            if len(cached_result) == len(df):
                return cached_result.copy()

        cls._cache_stats['misses'] += 1

        try:
            if df.empty or len(df) < 50:
                raise ValueError(f"Insufficient data for indicator calculation: {len(df)} bars")
            
            # Validate required columns
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
            
            # Create a copy to avoid modifying the original
            result_df = df.copy()
            
            # Validate data quality
            if result_df[['open', 'high', 'low', 'close', 'volume']].isnull().any().any():
                result_df = result_df.ffill()
            
            # Ensure positive values
            price_cols = ['open', 'high', 'low', 'close']
            for col in price_cols:
                if (result_df[col] <= 0).any():
                    result_df = result_df[result_df[col] > 0]
            
            if len(result_df) < 30:
                raise ValueError("Insufficient valid data after cleaning")
            
            # Basic trend indicators with error handling
            try:
                ema_fast = params.get('ema_fast', 12)
                ema_slow = params.get('ema_slow', 26)
                
                # Ensure we have valid EMA parameters
                if ema_fast >= ema_slow:
                    # If invalid, use defaults
                    ema_fast = 12
                    ema_slow = 26
                
                # Ensure parameters are within reasonable bounds for available data
                max_period = min(len(result_df) // 4, 200)  # Max 1/4 of data or 200 periods
                ema_fast = max(2, min(ema_fast, max_period // 2))
                ema_slow = max(ema_fast + 1, min(ema_slow, max_period))
                
                result_df['ema_fast'] = result_df['close'].ewm(span=ema_fast).mean()
                result_df['ema_slow'] = result_df['close'].ewm(span=ema_slow).mean()
                result_df['ema_signal'] = np.where(result_df['ema_fast'] > result_df['ema_slow'], 1, -1)
                
            except Exception as e:
                # Fallback to simple moving averages if EMA fails
                ema_fast = min(12, len(result_df) // 4)
                ema_slow = min(26, len(result_df) // 2)
                result_df['ema_fast'] = result_df['close'].rolling(window=ema_fast).mean()
                result_df['ema_slow'] = result_df['close'].rolling(window=ema_slow).mean()
                result_df['ema_signal'] = np.where(result_df['ema_fast'] > result_df['ema_slow'], 1, -1)
            
            # ATR calculation with detailed debugging
            try:
                atr_len = max(3, min(params.get('atr_len', 14), len(result_df) // 4))
                
                # Calculate ATR
                result_df['atr'] = result_df.ta.atr(length=atr_len)
                
                # Handle ATR calculation failures
                if result_df['atr'].isnull().all() or result_df['atr'].sum() == 0:
                    # Fallback ATR calculation
                    high_low = result_df['high'] - result_df['low']
                    high_close = abs(result_df['high'] - result_df['close'].shift())
                    low_close = abs(result_df['low'] - result_df['close'].shift())
                    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                    result_df['atr'] = tr.rolling(window=atr_len).mean()
                
                # Ensure ATR has positive values
                if result_df['atr'].isnull().all() or (result_df['atr'] <= 0).all():
                    result_df['atr'] = result_df['high'] - result_df['low']  # Simple range as fallback
                
            except Exception as e:
                # Ultimate fallback - use price range
                result_df['atr'] = result_df['high'] - result_df['low']
            
            # RSI calculation with validation
            try:
                rsi_len = max(3, min(params.get('rsi_len', 14), len(result_df) // 3))
                result_df['rsi'] = result_df.ta.rsi(length=rsi_len)
                
                # Validate RSI values
                if result_df['rsi'].isnull().all():
                    # Fallback RSI calculation
                    delta = result_df['close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_len).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_len).mean()
                    rs = gain / loss
                    result_df['rsi'] = 100 - (100 / (1 + rs))
                
                # Fill any remaining NaN values with neutral RSI
                result_df['rsi'] = result_df['rsi'].fillna(50)
                    
            except Exception as e:
                # Fallback to neutral RSI
                result_df['rsi'] = 50
            
            # Bollinger Bands with validation
            try:
                bb_len = max(5, min(params.get('bb_len', 20), len(result_df) // 3))
                bb_std = max(0.1, min(params.get('bb_std', 2.0), 10.0))
                
                bb = result_df.ta.bbands(length=bb_len, std=bb_std)
                if bb is not None and not bb.empty and len(bb.columns) >= 3:
                    result_df['bb_high'] = bb.iloc[:, 0]
                    result_df['bb_mid'] = bb.iloc[:, 1] 
                    result_df['bb_low'] = bb.iloc[:, 2]
                else:
                    # Fallback BB calculation
                    sma = result_df['close'].rolling(window=bb_len).mean()
                    std = result_df['close'].rolling(window=bb_len).std()
                    result_df['bb_high'] = sma + (std * bb_std)
                    result_df['bb_mid'] = sma
                    result_df['bb_low'] = sma - (std * bb_std)
                
                # Calculate BB position for reversion strategy
                result_df['bb_position'] = (result_df['close'] - result_df['bb_low']) / (result_df['bb_high'] - result_df['bb_low'])
                result_df['bb_position'] = result_df['bb_position'].fillna(0.5)
                    
            except Exception as e:
                # Simple fallback using close price
                result_df['bb_high'] = result_df['close'] * 1.02
                result_df['bb_mid'] = result_df['close']
                result_df['bb_low'] = result_df['close'] * 0.98
                # Calculate BB position for reversion strategy
                result_df['bb_position'] = (result_df['close'] - result_df['bb_low']) / (result_df['bb_high'] - result_df['bb_low'])
                result_df['bb_position'] = result_df['bb_position'].fillna(0.5)
            
            # MACD calculation with validation
            try:
                macd_fast = max(3, min(params.get('macd_fast', 12), len(result_df) // 6))
                macd_slow = max(macd_fast + 1, min(params.get('macd_slow', 26), len(result_df) // 3))
                macd_signal = max(2, min(params.get('macd_signal', 9), len(result_df) // 6))
                
                macd = result_df.ta.macd(fast=macd_fast, slow=macd_slow, signal=macd_signal)
                if macd is not None and not macd.empty and len(macd.columns) >= 3:
                    result_df['macd'] = macd.iloc[:, 0]
                    result_df['macd_signal'] = macd.iloc[:, 1]
                    result_df['macd_histogram'] = macd.iloc[:, 2]
                else:
                    # Fallback MACD calculation
                    ema_fast_macd = result_df['close'].ewm(span=macd_fast).mean()
                    ema_slow_macd = result_df['close'].ewm(span=macd_slow).mean()
                    result_df['macd'] = ema_fast_macd - ema_slow_macd
                    result_df['macd_signal'] = result_df['macd'].ewm(span=macd_signal).mean()
                    result_df['macd_histogram'] = result_df['macd'] - result_df['macd_signal']
                    
            except Exception as e:
                # Simple fallback
                result_df['macd'] = 0
                result_df['macd_signal'] = 0
                result_df['macd_histogram'] = 0
            
            # ADX calculation (if needed) with validation
            if 'adx_len' in params:
                try:
                    adx_len = max(5, min(params.get('adx_len', 14), len(result_df) // 3))
                    adx = result_df.ta.adx(length=adx_len)
                    if adx is not None and not adx.empty:
                        result_df['adx'] = adx.iloc[:, 0] if len(adx.columns) > 0 else 25
                    else:
                        result_df['adx'] = 25  # Neutral ADX value
                        
                except Exception as e:
                    result_df['adx'] = 25  # Fallback to neutral value
            
            # Strategy-specific signals
            try:
                IndicatorCalculator._add_strategy_signals(result_df, params)
            except Exception as e:
                raise ValueError(f"Strategy signal calculation failed: {str(e)}")
            
            # Final data validation
            result_df = result_df.replace([np.inf, -np.inf], np.nan)
            
            # Fill remaining NaN values using forward fill, then backward fill
            result_df = result_df.ffill()
            result_df = result_df.bfill()
            
            # Remove any rows that still have NaN values
            initial_len = len(result_df)
            result_df = result_df.dropna()
            final_len = len(result_df)
            
            if final_len < initial_len * 0.7:  # Lost more than 30% of data
                raise ValueError(f"Too much data lost during indicator calculation: {initial_len} -> {final_len}")
            
            if final_len < 20:
                raise ValueError(f"Insufficient data remaining after indicator calculation: {final_len} bars")

            # Cache the result (limit cache size to prevent memory issues)
            if len(cls._indicator_cache) < 50:
                cls._indicator_cache[cache_key] = result_df.copy()

            return result_df
            
        except Exception as e:
            error_msg = f"Indicator calculation failed: {str(e)}"
            if 'params' in locals():
                error_msg += f" with params: {params}"
            raise ValueError(error_msg)
    
    @staticmethod
    def _calculate_ema_indicators(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate EMA indicators."""
        if 'ema_fast' in params and 'ema_slow' in params:
            try:
                df.ta.ema(length=params['ema_fast'], append=True)
                df.ta.ema(length=params['ema_slow'], append=True)
            except Exception as e:
                print(f"Error calculating EMA: {e}")
    
    @staticmethod
    def _calculate_supertrend(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Supertrend indicator."""
        if 'st_len' in params and 'st_mult' in params:
            try:
                supertrend = df.ta.supertrend(
                    length=params['st_len'], 
                    multiplier=params['st_mult'], 
                    append=False
                )
                if supertrend is not None and not supertrend.empty:
                    df['supertrend_dir'] = supertrend.iloc[:, 1]
                else:
                    df['supertrend_dir'] = 0
            except Exception as e:
                print(f"Error calculating Supertrend: {e}")
                df['supertrend_dir'] = 0
    
    @staticmethod
    def _calculate_adx(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate ADX indicator."""
        if 'adx_len' in params:
            try:
                df.ta.adx(length=params['adx_len'], append=True)
            except Exception as e:
                print(f"Error calculating ADX: {e}")
    
    @staticmethod
    def _calculate_atr(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate ATR indicator."""
        if 'atr_len' in params:
            try:
                df[f"ATR_{params['atr_len']}"] = df.ta.atr(length=params['atr_len'])
            except Exception as e:
                print(f"Error calculating ATR: {e}")
    
    @staticmethod
    def _calculate_bollinger_bands(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Bollinger Bands."""
        if 'bb_len' in params and 'bb_std' in params:
            try:
                bbands = df.ta.bbands(
                    length=params['bb_len'], 
                    std=params['bb_std'], 
                    append=False
                )
                if bbands is not None and not bbands.empty:
                    df['bb_low'] = bbands.iloc[:, 0]
                    df['bb_mid'] = bbands.iloc[:, 1]
                    df['bb_high'] = bbands.iloc[:, 2]
                    # Calculate Bollinger Band position
                    df['bb_position'] = (df['close'] - df['bb_low']) / (df['bb_high'] - df['bb_low'])
                    df['bb_position'] = df['bb_position'].fillna(0.5)
            except Exception as e:
                print(f"Error calculating Bollinger Bands: {e}")
    
    @staticmethod
    def _calculate_rsi(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate RSI indicator."""
        if 'rsi_len' in params:
            try:
                df[f"RSI_{params['rsi_len']}"] = df.ta.rsi(length=params['rsi_len'])
            except Exception as e:
                print(f"Error calculating RSI: {e}")
    
    @staticmethod
    def _calculate_macd(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate MACD indicator."""
        if all(key in params for key in ['macd_fast', 'macd_slow', 'macd_signal']):
            try:
                macd = df.ta.macd(
                    fast=params['macd_fast'], 
                    slow=params['macd_slow'], 
                    signal=params['macd_signal'], 
                    append=False
                )
                if macd is not None and not macd.empty:
                    df['macd_line'] = macd.iloc[:, 0]
                    df['macd_signal'] = macd.iloc[:, 1]
                    df['macd_histogram'] = macd.iloc[:, 2]
            except Exception as e:
                print(f"Error calculating MACD: {e}")
    
    @staticmethod
    def _calculate_stochastic(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Stochastic oscillator."""
        if 'stoch_k' in params and 'stoch_d' in params:
            try:
                stoch = df.ta.stoch(k=params['stoch_k'], d=params['stoch_d'], append=False)
                if stoch is not None and not stoch.empty:
                    df['stoch_k'] = stoch.iloc[:, 0]
                    df['stoch_d'] = stoch.iloc[:, 1]
            except Exception as e:
                print(f"Error calculating Stochastic: {e}")
    
    @staticmethod
    def _calculate_williams_r(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Williams %R indicator."""
        if 'willr_len' in params:
            try:
                df[f"WILLR_{params['willr_len']}"] = df.ta.willr(length=params['willr_len'])
            except Exception as e:
                print(f"Error calculating Williams %R: {e}")
    
    @staticmethod
    def _calculate_cci(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Commodity Channel Index."""
        if 'cci_len' in params:
            try:
                df[f"CCI_{params['cci_len']}"] = df.ta.cci(length=params['cci_len'])
            except Exception as e:
                print(f"Error calculating CCI: {e}")
    
    @staticmethod
    def _calculate_roc(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Rate of Change."""
        if 'roc_len' in params:
            try:
                df[f"ROC_{params['roc_len']}"] = df.ta.roc(length=params['roc_len'])
            except Exception as e:
                print(f"Error calculating ROC: {e}")
    
    @staticmethod
    def _calculate_volume_indicators(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate volume-based indicators."""
        try:
            if 'vol_sma_len' in params:
                df['volume_sma'] = df['volume'].rolling(window=params['vol_sma_len']).mean()
            
            # Volume ratio (current vs average)
            if 'volume' in df.columns:
                df['volume_ratio'] = df['volume'] / df['volume'].rolling(window=20).mean()
                df['volume_ratio'] = df['volume_ratio'].fillna(1.0)
                
                # Price Volume Trend
                price_change = (df['close'] - df['close'].shift(1)) / df['close'].shift(1)
                df['price_volume_trend'] = (price_change * df['volume']).cumsum()
                df['price_volume_trend'] = df['price_volume_trend'].fillna(0)
                
        except Exception as e:
            print(f"Error calculating volume indicators: {e}")
    
    @staticmethod
    def _calculate_mfi(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate Money Flow Index."""
        if 'mfi_len' in params:
            try:
                mfi_series = df.ta.mfi(length=params['mfi_len'])
                if mfi_series is not None:
                    df[f"MFI_{params['mfi_len']}"] = mfi_series
            except Exception as e:
                print(f"Error calculating MFI: {e}")
    
    @staticmethod
    def _calculate_market_regime_indicators(df: pd.DataFrame, params: Dict[str, Any]):
        """Calculate market regime detection indicators."""
        try:
            # Price range for trend strength calculation
            df['price_range_20'] = df['high'].rolling(20).max() - df['low'].rolling(20).min()
            df['trend_strength'] = abs(df['close'] - df['close'].shift(20)) / df['price_range_20']
            df['trend_strength'] = df['trend_strength'].fillna(0.5)
            
            # Volatility percentile ranking
            if 'atr_len' in params:
                atr_col = f"ATR_{params['atr_len']}"
                if atr_col in df.columns:
                    df['volatility_ratio'] = df[atr_col] / df['close']
                    df['volatility_ratio'] = df['volatility_ratio'].fillna(0.02)
                    
                    # Volatility percentile (rolling 100 periods)
                    df['volatility_percentile'] = df[atr_col].rolling(100).rank(pct=True) * 100
                    df['volatility_percentile'] = df['volatility_percentile'].fillna(50)
            
        except Exception as e:
            print(f"Error calculating market regime indicators: {e}")
    
    @staticmethod
    def _calculate_price_action_patterns(df: pd.DataFrame):
        """Calculate price action pattern indicators."""
        try:
            # Higher highs and lower lows
            df['higher_high'] = (
                (df['high'] > df['high'].shift(1)) & 
                (df['high'].shift(1) > df['high'].shift(2))
            )
            
            df['lower_low'] = (
                (df['low'] < df['low'].shift(1)) & 
                (df['low'].shift(1) < df['low'].shift(2))
            )
            
            # Doji pattern detection
            body_size = abs(df['close'] - df['open'])
            candle_range = df['high'] - df['low']
            df['doji'] = (body_size / (candle_range + 1e-8)) < 0.1
            
            # Fill NaN values
            df['higher_high'] = df['higher_high'].fillna(False)
            df['lower_low'] = df['lower_low'].fillna(False)
            df['doji'] = df['doji'].fillna(False)
            
        except Exception as e:
            print(f"Error calculating price action patterns: {e}")
    
    @staticmethod
    def add_mta_trend(df: pd.DataFrame, mta_df: pd.DataFrame) -> pd.DataFrame:
        """
        Add Multi-Timeframe Analysis (MTA) trend to the main dataframe.
        
        Args:
            df: Main timeframe dataframe
            mta_df: Higher timeframe dataframe with trend analysis
            
        Returns:
            DataFrame with MTA trend column added
        """
        try:
            if mta_df.empty:
                df['mta_trend'] = 1  # Default to bullish if no MTA data
                return df
            
            # Calculate trend from higher timeframe
            if 'EMA_50' in mta_df.columns and 'EMA_200' in mta_df.columns:
                mta_trend = np.where(mta_df['EMA_50'] > mta_df['EMA_200'], 1, -1)
            else:
                # Fallback to simple price trend
                mta_trend = np.where(mta_df['close'] > mta_df['close'].shift(10), 1, -1)
            
            mta_df = mta_df.copy()
            mta_df['mta_trend'] = mta_trend
            
            # Resample to match main timeframe
            df_resampled = mta_df['mta_trend'].resample(
                pd.Timedelta(minutes=15)  # Assuming main timeframe is 15m
            ).ffill()
            
            # Merge with main dataframe
            df = df.join(df_resampled, how='left')
            df['mta_trend'] = df['mta_trend'].ffill().fillna(1)
            
            return df
            
        except Exception as e:
            print(f"Error adding MTA trend: {e}")
            df['mta_trend'] = 1  # Default to bullish on error
            return df
    
    @staticmethod
    def validate_indicators(df: pd.DataFrame, required_indicators: list) -> bool:
        """
        Validate that all required indicators are present in the dataframe.
        
        Args:
            df: DataFrame to validate
            required_indicators: List of required indicator column names
            
        Returns:
            True if all indicators are present, False otherwise
        """
        missing_indicators = [ind for ind in required_indicators if ind not in df.columns]
        
        if missing_indicators:
            print(f"Missing indicators: {missing_indicators}")
            return False
        
        return True 

    @staticmethod
    def _add_strategy_signals(df: pd.DataFrame, params: Dict[str, Any]) -> None:
        """Add basic strategy signals to the dataframe."""
        try:
            # Add basic trend signal based on EMA
            if 'ema_fast' in df.columns and 'ema_slow' in df.columns:
                df['trend_signal'] = np.where(df['ema_fast'] > df['ema_slow'], 1, -1)
            else:
                df['trend_signal'] = 0
            
            # Add volatility signal based on ATR
            if 'atr' in df.columns:
                atr_sma = df['atr'].rolling(window=14).mean()
                df['volatility_signal'] = np.where(df['atr'] > atr_sma * 1.5, 1, 0)
            else:
                df['volatility_signal'] = 0
            
            # Add momentum signal based on RSI
            if 'rsi' in df.columns:
                df['momentum_signal'] = np.where(
                    df['rsi'] > 70, -1,  # Overbought
                    np.where(df['rsi'] < 30, 1, 0)  # Oversold
                )
            else:
                df['momentum_signal'] = 0
            
            # Add volume signal if available
            if 'volume' in df.columns:
                volume_sma = df['volume'].rolling(window=20).mean()
                df['volume_signal'] = np.where(df['volume'] > volume_sma * 1.5, 1, 0)
            else:
                df['volume_signal'] = 0
                
        except Exception as e:
            # If signal calculation fails, add neutral signals
            df['trend_signal'] = 0
            df['volatility_signal'] = 0
            df['momentum_signal'] = 0
            df['volume_signal'] = 0 