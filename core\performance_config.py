"""
Performance optimization configuration for Ultratrader system.

This module contains optimized settings and utilities for maximum performance.
"""

import os
import multiprocessing
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class PerformanceConfig:
    """Performance optimization settings."""
    
    # Parallel processing settings
    max_workers: int = min(multiprocessing.cpu_count(), 8)  # Limit to prevent resource exhaustion
    enable_parallel_optimization: bool = True
    enable_parallel_walk_forward: bool = True
    
    # Memory management
    max_memory_cache_size: int = 100  # Maximum cached items
    enable_memory_monitoring: bool = True
    memory_cleanup_threshold: float = 0.8  # Clean up when 80% memory used
    
    # Numba optimization
    enable_numba_cache: bool = True
    numba_parallel: bool = True
    numba_fastmath: bool = True
    
    # Database optimization
    optuna_storage_engine: str = "sqlite"  # Can be changed to PostgreSQL for better performance
    optuna_connection_pool_size: int = 10
    enable_optuna_pruning: bool = True
    
    # I/O optimization
    use_feather_format: bool = True  # Faster than CSV
    enable_compression: bool = False  # Trade-off between speed and storage
    batch_size: int = 1000  # For batch processing
    
    # Optimization algorithm settings
    optuna_sampler_startup_trials: int = 10
    optuna_sampler_ei_candidates: int = 20
    optuna_pruner_warmup_steps: int = 10
    optuna_pruner_interval_steps: int = 5
    
    # Cache settings
    indicator_cache_size: int = 50
    data_cache_size: int = 10
    enable_persistent_cache: bool = True


class PerformanceMonitor:
    """Monitor and optimize system performance."""
    
    def __init__(self):
        self.config = PerformanceConfig()
        self._memory_usage = {}
        self._timing_stats = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation."""
        import time
        self._timing_stats[operation] = {'start': time.time()}
    
    def end_timer(self, operation: str):
        """End timing an operation."""
        import time
        if operation in self._timing_stats:
            self._timing_stats[operation]['end'] = time.time()
            self._timing_stats[operation]['duration'] = (
                self._timing_stats[operation]['end'] - 
                self._timing_stats[operation]['start']
            )
    
    def get_timing_stats(self) -> Dict[str, float]:
        """Get timing statistics."""
        return {
            op: stats.get('duration', 0) 
            for op, stats in self._timing_stats.items()
        }
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """Check current memory usage."""
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent()
        }
    
    def should_cleanup_memory(self) -> bool:
        """Check if memory cleanup is needed."""
        if not self.config.enable_memory_monitoring:
            return False
        
        memory_info = self.check_memory_usage()
        return memory_info['percent'] > self.config.memory_cleanup_threshold * 100
    
    def optimize_for_system(self):
        """Optimize configuration based on system capabilities."""
        import psutil
        
        # Adjust based on available CPU cores
        cpu_count = psutil.cpu_count()
        if cpu_count <= 4:
            self.config.max_workers = 2
            self.config.enable_parallel_walk_forward = False
        elif cpu_count >= 16:
            self.config.max_workers = min(12, cpu_count)
        
        # Adjust based on available memory
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 8:
            self.config.max_memory_cache_size = 20
            self.config.indicator_cache_size = 20
            self.config.data_cache_size = 5
        elif memory_gb >= 32:
            self.config.max_memory_cache_size = 200
            self.config.indicator_cache_size = 100
            self.config.data_cache_size = 20
    
    def get_optimization_recommendations(self) -> Dict[str, str]:
        """Get performance optimization recommendations."""
        recommendations = {}
        
        memory_info = self.check_memory_usage()
        if memory_info['percent'] > 70:
            recommendations['memory'] = "Consider reducing cache sizes or enabling compression"
        
        timing_stats = self.get_timing_stats()
        if 'optimization' in timing_stats and timing_stats['optimization'] > 300:
            recommendations['optimization'] = "Consider reducing trial counts or enabling more aggressive pruning"
        
        if 'data_fetch' in timing_stats and timing_stats['data_fetch'] > 30:
            recommendations['data'] = "Consider using persistent caching or reducing history days"
        
        return recommendations


# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Auto-optimize on import
performance_monitor.optimize_for_system()


def enable_performance_optimizations():
    """Enable all performance optimizations."""
    import warnings
    import pandas as pd
    
    # Suppress warnings for better performance
    warnings.filterwarnings('ignore')
    
    # Pandas optimizations
    pd.options.mode.chained_assignment = None
    pd.options.compute.use_bottleneck = True
    pd.options.compute.use_numexpr = True
    
    # Set environment variables for better performance
    os.environ['NUMBA_CACHE_DIR'] = '.numba_cache'
    os.environ['NUMBA_NUM_THREADS'] = str(performance_monitor.config.max_workers)
    
    print("Performance optimizations enabled")


def get_optimal_batch_size(data_size: int, available_memory_mb: float) -> int:
    """Calculate optimal batch size based on data size and available memory."""
    # Rough estimation: assume each data point takes ~1KB
    estimated_memory_per_item = 1  # KB
    max_items_in_memory = int(available_memory_mb * 1024 * 0.5)  # Use 50% of available memory
    
    optimal_batch = min(
        max_items_in_memory // estimated_memory_per_item,
        data_size,
        performance_monitor.config.batch_size
    )
    
    return max(1, optimal_batch)


def should_use_parallel_processing(task_count: int, task_complexity: str = 'medium') -> bool:
    """Determine if parallel processing should be used."""
    if not performance_monitor.config.enable_parallel_optimization:
        return False
    
    # Complexity factors
    complexity_factors = {
        'low': 2,
        'medium': 4,
        'high': 8
    }
    
    min_tasks_for_parallel = complexity_factors.get(task_complexity, 4)
    
    return (
        task_count >= min_tasks_for_parallel and 
        performance_monitor.config.max_workers > 1
    )
