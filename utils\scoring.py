"""
Scoring system for Ultratrader optimization system.

Advanced scoring algorithms including the enhanced Quantum Score V3
and comprehensive performance evaluation metrics.
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass

from core.config import config


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    sortino_ratio: float
    profit_factor: float
    max_drawdown: float
    total_return: float
    win_rate: float
    trade_count: int
    avg_win: float
    avg_loss: float
    sharpe_ratio: float
    calmar_ratio: float
    recovery_factor: float
    var_95: float
    expected_shortfall: float


@dataclass
class QuantumScoreComponents:
    """Components of the Quantum Score calculation."""
    risk_adjusted_return: float
    consistency_factor: float
    efficiency_multiplier: float
    risk_penalty: float
    stability_bonus: float
    final_score: float


class ScoringSystem:
    """
    Advanced scoring system for strategy evaluation.
    
    Features:
    - Quantum Score V3 with enhanced risk assessment
    - Multi-dimensional performance evaluation
    - Risk-adjusted metrics
    - Robustness scoring
    - Market regime adaptation scoring
    """
    
    @staticmethod
    def calculate_quantum_score_v3(
        sortino_ratio: float,
        profit_factor: float,
        max_drawdown: float,
        total_return: float,
        win_rate: float,
        trade_count: int,
        avg_win: float = 0.0,
        avg_loss: float = 0.0
    ) -> <PERSON><PERSON>[float, QuantumScoreComponents]:
        """
        Calculate enhanced Quantum Score V3 with improved risk assessment.
        
        This version addresses the zero-score issue by:
        - Better handling of negative Sortino ratios
        - More flexible calculation that rewards risk management
        - Considering strategies that minimize losses in difficult markets
        - Normalized components for better scoring consistency
        
        Args:
            sortino_ratio: Sortino ratio of the strategy
            profit_factor: Profit factor (gross gains / gross losses)
            max_drawdown: Maximum drawdown (0-1)
            total_return: Total return percentage
            win_rate: Win rate (0-1)
            trade_count: Number of trades
            avg_win: Average winning trade amount
            avg_loss: Average losing trade amount
            
        Returns:
            Tuple of (quantum_score, score_components)
        """
        
        # Handle edge cases
        if trade_count < config.optimization.min_trades:
            return 0.0, QuantumScoreComponents(0, 0, 0, 0, 0, 0)
        
        # 1. Risk-Adjusted Return Component (40% weight)
        # Enhanced to handle negative Sortino ratios more gracefully
        if sortino_ratio <= -5:
            # Very poor performance - heavy penalty
            risk_adjusted_base = -10
        elif sortino_ratio < 0:
            # Negative but not terrible - moderate penalty
            risk_adjusted_base = sortino_ratio * 2  # Amplify negative impact
        else:
            # Positive Sortino - normal calculation
            risk_adjusted_base = min(sortino_ratio, 10)  # Cap at 10 to prevent extreme scores
        
        # Add return consideration - heavily reward positive returns
        return_factor = max(total_return, -0.2)  # Don't penalize beyond -20%
        
        # Exponential bonus for high returns to target 5% monthly (adjusted for ~16 day period)
        monthly_target = 0.05  # 5% monthly
        period_target = monthly_target * (16/30)  # ~2.67% for 16-day period
        
        if total_return > period_target:  
            return_bonus = (total_return - period_target) * 500  # HUGE bonus for exceeding 2.67% target
        elif total_return > 0.015:  # 1.5% minimum
            return_bonus = total_return * 100  # Good bonus for high returns
        elif total_return > 0.008:  # 0.8% minimum
            return_bonus = total_return * 50   # Moderate bonus for good returns
        else:
            return_bonus = 0
        
        risk_adjusted_return = (risk_adjusted_base * 0.5) + (return_factor * 50) + return_bonus  # 50x multiplier + bonus
        
        # 2. Consistency Factor (25% weight)
        # Improved to consider profit factor and win rate together
        if profit_factor <= 0:
            consistency_base = -5
        else:
            # Normalize profit factor (optimal range 1.2-3.0)
            pf_normalized = min(max(profit_factor - 1, 0), 2) / 2  # 0-1 range
            consistency_base = pf_normalized * 10
        
        # Win rate bonus (30-70% is good range)
        wr_normalized = max(min(win_rate, 0.8), 0.2)  # Clamp between 20-80%
        win_rate_bonus = (wr_normalized - 0.2) / 0.6 * 5  # 0-5 bonus
        
        consistency_factor = consistency_base + win_rate_bonus
        
        # 3. Efficiency Multiplier (20% weight)
        # Reward strategies that achieve good results with fewer trades
        if trade_count <= 5:
            efficiency_base = 1.0
        elif trade_count <= 20:
            efficiency_base = 1.2
        elif trade_count <= 50:
            efficiency_base = 1.1
        else:
            efficiency_base = 1.0
        
        # Risk-reward efficiency
        if avg_loss > 0 and avg_win > 0:
            rr_ratio = avg_win / avg_loss
            rr_multiplier = min(max(rr_ratio / 2, 0.5), 2.0)  # 0.5-2.0 range
        else:
            rr_multiplier = 1.0
        
        efficiency_multiplier = efficiency_base * rr_multiplier * 10  # Scale to 0-20 range
        
        # 4. Risk Penalty (15% weight)
        # Enhanced drawdown penalty with graduated scaling
        if max_drawdown <= 0.05:  # <= 5%
            dd_penalty = 0
        elif max_drawdown <= 0.10:  # 5-10%
            dd_penalty = (max_drawdown - 0.05) * 20  # Light penalty
        elif max_drawdown <= 0.15:  # 10-15%
            dd_penalty = 1 + (max_drawdown - 0.10) * 40  # Moderate penalty
        else:  # > 15%
            dd_penalty = 3 + (max_drawdown - 0.15) * 100  # Heavy penalty
        
        risk_penalty = min(dd_penalty, 15)  # Cap penalty at 15 points
        
        # 5. Stability Bonus (10% weight)
        # Bonus for stable, conservative strategies
        stability_bonus = 0
        
        # Low drawdown bonus
        if max_drawdown < 0.05:
            stability_bonus += 3
        elif max_drawdown < 0.08:
            stability_bonus += 1
        
        # Consistent profitability bonus
        if total_return > 0 and sortino_ratio > 0.5:
            stability_bonus += 2
        
        # Conservative win rate bonus
        if 0.4 <= win_rate <= 0.7:  # Sweet spot for win rate
            stability_bonus += 1
        
        # Calculate final score with weights - strong focus on profitability but balanced
        weights = {
            'risk_adjusted_return': 0.60,  # Strong focus on returns
            'consistency_factor': 0.15,     # Some consistency requirement
            'efficiency_multiplier': 0.15,  # Some efficiency focus
            'risk_penalty': 0.05,           # Light risk penalty
            'stability_bonus': 0.05         # Light stability requirement
        }
        
        quantum_score = (
            risk_adjusted_return * weights['risk_adjusted_return'] +
            consistency_factor * weights['consistency_factor'] +
            efficiency_multiplier * weights['efficiency_multiplier'] -
            risk_penalty * weights['risk_penalty'] +
            stability_bonus * weights['stability_bonus']
        )
        
        # Ensure minimum score floor for strategies that preserve capital
        if total_return >= -0.05 and max_drawdown < 0.1:  # Lost < 5% with < 10% DD
            quantum_score = max(quantum_score, 1.0)  # Minimum score of 1.0
        
        components = QuantumScoreComponents(
            risk_adjusted_return=risk_adjusted_return,
            consistency_factor=consistency_factor,
            efficiency_multiplier=efficiency_multiplier,
            risk_penalty=risk_penalty,
            stability_bonus=stability_bonus,
            final_score=quantum_score
        )
        
        return max(quantum_score, 0.0), components
    
    @staticmethod
    def calculate_walk_forward_score(
        in_sample_metrics: PerformanceMetrics,
        out_of_sample_metrics: PerformanceMetrics,
        stability_threshold: float = 0.5
    ) -> Tuple[float, Dict[str, float]]:
        """
        Calculate walk-forward analysis score comparing in-sample vs out-of-sample performance.
        
        Args:
            in_sample_metrics: Performance metrics from training period
            out_of_sample_metrics: Performance metrics from validation period
            stability_threshold: Minimum ratio of OOS/IS performance for stability
            
        Returns:
            Tuple of (walk_forward_score, detailed_metrics)
        """
        
        # Calculate stability ratios for key metrics
        def safe_ratio(oos_value: float, is_value: float) -> float:
            if is_value <= 0:
                return 0.0 if oos_value <= 0 else 1.0
            return max(oos_value / is_value, 0.0)
        
        # Key stability metrics
        sortino_ratio_stability = safe_ratio(
            out_of_sample_metrics.sortino_ratio,
            in_sample_metrics.sortino_ratio
        )
        
        profit_factor_stability = safe_ratio(
            out_of_sample_metrics.profit_factor,
            in_sample_metrics.profit_factor
        )
        
        return_stability = safe_ratio(
            out_of_sample_metrics.total_return,
            in_sample_metrics.total_return
        )
        
        # Drawdown comparison (lower is better, so inverse ratio)
        if in_sample_metrics.max_drawdown > 0:
            drawdown_improvement = max(
                1 - (out_of_sample_metrics.max_drawdown / in_sample_metrics.max_drawdown),
                -1.0
            )
        else:
            drawdown_improvement = 0.0
        
        # Trade count consistency
        trade_count_ratio = safe_ratio(
            out_of_sample_metrics.trade_count,
            in_sample_metrics.trade_count
        )
        
        # Calculate weighted stability score
        stability_score = (
            sortino_ratio_stability * 0.30 +
            profit_factor_stability * 0.25 +
            return_stability * 0.20 +
            (1 + drawdown_improvement) * 0.15 +
            min(trade_count_ratio, 2.0) * 0.10  # Cap trade count ratio at 2.0
        )
        
        # Apply minimum stability threshold
        if stability_score < stability_threshold:
            walk_forward_score = stability_score * 0.5  # Heavy penalty for unstable strategies
        else:
            walk_forward_score = stability_score
        
        # Bonus for strategies that improve out-of-sample
        if out_of_sample_metrics.sortino_ratio > in_sample_metrics.sortino_ratio:
            walk_forward_score *= 1.1  # 10% bonus
        
        detailed_metrics = {
            'sortino_stability': sortino_ratio_stability,
            'profit_factor_stability': profit_factor_stability,
            'return_stability': return_stability,
            'drawdown_improvement': drawdown_improvement,
            'trade_count_ratio': trade_count_ratio,
            'overall_stability': stability_score
        }
        
        return min(walk_forward_score, 2.0), detailed_metrics  # Cap at 2.0
    
    @staticmethod
    def calculate_robustness_score(
        parameter_sensitivity: Dict[str, float],
        market_regime_performance: Dict[str, float]
    ) -> float:
        """
        Calculate robustness score based on parameter sensitivity and market regime performance.
        
        Args:
            parameter_sensitivity: Dict of parameter -> sensitivity score
            market_regime_performance: Dict of regime -> performance score
            
        Returns:
            Robustness score (0-1)
        """
        
        # Parameter sensitivity score (lower sensitivity = higher score)
        if parameter_sensitivity:
            avg_sensitivity = np.mean(list(parameter_sensitivity.values()))
            sensitivity_score = max(1 - avg_sensitivity, 0.0)
        else:
            sensitivity_score = 0.5  # Default if no sensitivity data
        
        # Market regime consistency score
        if market_regime_performance:
            regime_scores = list(market_regime_performance.values())
            if len(regime_scores) > 1:
                regime_consistency = 1 - (np.std(regime_scores) / (np.mean(regime_scores) + 1e-8))
                regime_consistency = max(regime_consistency, 0.0)
            else:
                regime_consistency = 0.5
        else:
            regime_consistency = 0.5
        
        # Combined robustness score
        robustness_score = (sensitivity_score * 0.6 + regime_consistency * 0.4)
        
        return max(min(robustness_score, 1.0), 0.0)
    
    @staticmethod
    def calculate_comprehensive_score(
        quantum_score: float,
        walk_forward_score: float,
        robustness_score: float,
        weights: Optional[Dict[str, float]] = None
    ) -> Tuple[float, Dict[str, float]]:
        """
        Calculate comprehensive strategy score combining all evaluation dimensions.
        
        Args:
            quantum_score: Quantum Score V3
            walk_forward_score: Walk-forward analysis score
            robustness_score: Robustness and stability score
            weights: Optional custom weights for score components
            
        Returns:
            Tuple of (comprehensive_score, component_scores)
        """
        
        if weights is None:
            weights = {
                'quantum': 0.50,      # Core performance
                'walk_forward': 0.35,  # Out-of-sample stability
                'robustness': 0.15     # Parameter and regime robustness
            }
        
        # Normalize scores to 0-100 scale
        normalized_quantum = min(max(quantum_score * 10, 0), 100)  # Quantum typically 0-10
        normalized_walk_forward = min(max(walk_forward_score * 50, 0), 100)  # WF typically 0-2
        normalized_robustness = min(max(robustness_score * 100, 0), 100)  # Robustness 0-1
        
        comprehensive_score = (
            normalized_quantum * weights['quantum'] +
            normalized_walk_forward * weights['walk_forward'] +
            normalized_robustness * weights['robustness']
        )
        
        component_scores = {
            'quantum_score': normalized_quantum,
            'walk_forward_score': normalized_walk_forward,
            'robustness_score': normalized_robustness,
            'comprehensive_score': comprehensive_score
        }
        
        return comprehensive_score, component_scores
    
    @staticmethod
    def validate_strategy_metrics(
        sortino_ratio: float,
        profit_factor: float,
        total_return: float,
        max_drawdown: float,
        trade_count: int
    ) -> Tuple[bool, str]:
        """
        Validate if a strategy meets minimum criteria for further evaluation.
        
        Args:
            sortino_ratio: Sortino ratio
            profit_factor: Profit factor
            total_return: Total return
            max_drawdown: Maximum drawdown
            trade_count: Number of trades
            
        Returns:
            Tuple of (is_valid, reason)
        """
        
        # Very lenient validation for initial testing
        
        # Minimum trade count - very low
        if trade_count < 1:
            return False, f"No trades executed: {trade_count}"
        
        # Maximum drawdown limit - very high
        if max_drawdown > 0.75:  # 75% max drawdown
            return False, f"Extreme drawdown: {max_drawdown:.1%} > 75%"
        
        # Accept any strategy that generated trades with reasonable metrics
        # (removed Sortino, profit factor, and return restrictions for now)
        
        return True, "Strategy meets all criteria"
    
    @staticmethod
    def rank_strategies(
        strategies: Dict[str, Dict[str, Any]],
        ranking_method: str = 'quantum'
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Rank strategies based on specified method.
        
        Args:
            strategies: Dict of strategy_id -> strategy_data
            ranking_method: 'quantum', 'sortino', 'comprehensive'
            
        Returns:
            List of (strategy_id, strategy_data) tuples sorted by rank
        """
        
        if ranking_method == 'quantum':
            key_func = lambda x: x[1].get('quantum_score', 0)
        elif ranking_method == 'sortino':
            key_func = lambda x: x[1].get('sortino_ratio', 0)
        elif ranking_method == 'comprehensive':
            key_func = lambda x: x[1].get('comprehensive_score', 0)
        else:
            raise ValueError(f"Unknown ranking method: {ranking_method}")
        
        return sorted(strategies.items(), key=key_func, reverse=True)


# Convenience function for backward compatibility
def calculate_quantum_score_v3(
    sortino_ratio: float,
    profit_factor: float,
    max_drawdown: float,
    total_return: float,
    win_rate: float,
    trade_count: int,
    avg_win: float = 0.0,
    avg_loss: float = 0.0
) -> float:
    """Convenience function that returns only the score (backward compatibility)."""
    score, _ = ScoringSystem.calculate_quantum_score_v3(
        sortino_ratio, profit_factor, max_drawdown, total_return,
        win_rate, trade_count, avg_win, avg_loss
    )
    return score 