from typing import List, Dict, Any
from core.config import config
import logging

logger = logging.getLogger(__name__)

def log_rejection(symbol: str, mode: str, reason: str):
    logger.info(f"REJEITADA {symbol} {mode}: {reason}")

def validate_strategies_sota(results: List[Dict[str, Any]], cfg=config) -> List[Dict[str, Any]]:
    """
    Valida estratégias com critérios SOTA e loga motivo de rejeição.
    Exige pelo menos 5% de lucro mensal OOS.
    """
    # Critérios dinâmicos para 5% ao mês
    oos_period_months = getattr(cfg.optimization, 'oos_period_months', 1)
    min_oos_return = getattr(cfg.optimization, 'min_oos_return_monthly', 0.05) * oos_period_months
    criteria = {
        'min_total_return': getattr(cfg.optimization, 'min_total_return', 0.01),
        'max_drawdown': getattr(cfg.optimization, 'max_drawdown', 0.25),
        'min_trades': getattr(cfg.optimization, 'min_trades', 10),
        'min_sortino_oos': getattr(cfg.optimization, 'min_sortino_oos', 0.5),
        'min_oos_return': min_oos_return,
        'max_degradation': getattr(cfg.optimization, 'max_degradation', 0.3),
    }
    valid = []
    for strat in results:
        perf = strat.get('optimization_metrics', {})
        wf = strat.get('walk_forward_metrics', {})
        symbol = strat.get('symbol')
        mode = strat.get('strategy_mode')
        oos_ret = wf.get('cumulative_oos_return', 0)
        if perf.get('trade_count', 0) < criteria['min_trades']:
            log_rejection(symbol, mode, f"Poucos trades: {perf.get('trade_count', 0)}")
            continue
        if perf.get('max_drawdown', 1) > criteria['max_drawdown']:
            log_rejection(symbol, mode, f"Drawdown alto: {perf.get('max_drawdown', 1):.2f}")
            continue
        if oos_ret < criteria['min_oos_return']:
            log_rejection(symbol, mode, f"Retorno OOS baixo: {oos_ret:.2f} (mínimo {criteria['min_oos_return']:.2f})")
            continue
        if wf.get('avg_oos_sortino', 0) < criteria['min_sortino_oos']:
            log_rejection(symbol, mode, f"Sortino OOS baixo: {wf.get('avg_oos_sortino', 0):.2f}")
            continue
        degradation = perf.get('total_return', 0) - oos_ret
        if degradation > criteria['max_degradation']:
            log_rejection(symbol, mode, f"Degradação IS->OOS alta: {degradation:.2f}")
            continue
        logger.info(f"APROVADA {symbol} {mode}: Retorno OOS={oos_ret:.2f} (mínimo {criteria['min_oos_return']:.2f})")
        valid.append(strat)
    return valid

def score_strategies_sota(valid_results: List[Dict[str, Any]], cfg=config) -> List[Dict[str, Any]]:
    """
    Calcula score SOTA para cada estratégia válida.
    Pesos priorizam retorno OOS e penalizam drawdown.
    """
    weights = getattr(cfg.optimization, 'sota_weights', {
        'oos_return': 0.5,
        'drawdown': -0.3,
        'sortino_oos': 0.2,
        'consistency': 0.15,
        'trades': 0.05
    })
    for strat in valid_results:
        perf = strat.get('optimization_metrics', {})
        wf = strat.get('walk_forward_metrics', {})
        consistency = 1.0 - abs(perf.get('total_return', 0) - wf.get('cumulative_oos_return', 0))
        score = (
            wf.get('cumulative_oos_return', 0) * weights['oos_return'] +
            -perf.get('max_drawdown', 1) * abs(weights['drawdown']) +
            wf.get('avg_oos_sortino', 0) * weights['sortino_oos'] +
            consistency * weights['consistency'] +
            min(perf.get('trade_count', 0) / 100, 1.0) * weights['trades']
        )
        strat['sota_score'] = score
        strat['sota_consistency'] = consistency
    return valid_results

def select_top_strategies_sota(scored_results: List[Dict[str, Any]], cfg=config) -> List[Dict[str, Any]]:
    """
    Seleciona as top N estratégias pelo score SOTA.
    """
    top_n = getattr(cfg.optimization, 'top_n', 5)
    top = sorted(scored_results, key=lambda x: x['sota_score'], reverse=True)[:top_n]
    for i, strat in enumerate(top, 1):
        logger.info(f"TOP {i}: {strat['symbol']} {strat['strategy_mode']} | Score: {strat['sota_score']:.3f}")
    return top 