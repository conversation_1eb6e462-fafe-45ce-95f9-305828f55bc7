"""
Plotting and visualization utilities for Ultratrader optimization system.

Provides chart generation for:
- Strategy performance visualization
- Technical indicator plots  
- Optimization result analysis
- Walk-forward analysis results
- Risk metrics visualization
"""

import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple, Any
import warnings

# Suppress matplotlib warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

class ChartGenerator:
    """
    Comprehensive chart generation for trading system analysis.
    
    Features:
    - Strategy performance charts
    - Technical indicator visualization
    - Optimization result analysis
    - Risk metrics plotting
    - Interactive Plotly charts
    """
    
    def __init__(self, style: str = 'seaborn-v0_8', figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize chart generator.
        
        Args:
            style: Matplotlib style
            figsize: Default figure size
        """
        self.style = style
        self.figsize = figsize
        self.colors = {
            'buy': '#00ff00',
            'sell': '#ff0000', 
            'price': '#1f77b4',
            'signal': '#ff7f0e',
            'volume': '#2ca02c',
            'profit': '#00ff00',
            'loss': '#ff0000',
            'neutral': '#888888'
        }
        
        # Set matplotlib style
        try:
            plt.style.use(self.style)
        except:
            plt.style.use('default')
    
    def plot_strategy_performance(self, data: pd.DataFrame, trades: pd.DataFrame, 
                                title: str = "Strategy Performance") -> plt.Figure:
        """
        Plot strategy performance with price, signals, and equity curve.
        
        Args:
            data: Price data with indicators
            trades: Trade results
            title: Chart title
            
        Returns:
            matplotlib Figure
        """
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        fig.suptitle(title, fontsize=16, fontweight='bold')
        
        # Price chart with signals
        ax1 = axes[0]
        ax1.plot(data.index, data['close'], label='Close Price', color=self.colors['price'], linewidth=1)
        
        # Plot buy/sell signals if available
        if 'signal' in data.columns:
            buy_signals = data[data['signal'] == 1]
            sell_signals = data[data['signal'] == -1]
            
            ax1.scatter(buy_signals.index, buy_signals['close'], 
                       color=self.colors['buy'], marker='^', s=60, label='Buy Signal', zorder=5)
            ax1.scatter(sell_signals.index, sell_signals['close'], 
                       color=self.colors['sell'], marker='v', s=60, label='Sell Signal', zorder=5)
        
        # Add technical indicators if available
        if 'ema_fast' in data.columns:
            ax1.plot(data.index, data['ema_fast'], label='EMA Fast', alpha=0.7, linewidth=1)
        if 'ema_slow' in data.columns:
            ax1.plot(data.index, data['ema_slow'], label='EMA Slow', alpha=0.7, linewidth=1)
            
        ax1.set_title('Price Chart with Signals')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Volume chart
        ax2 = axes[1]
        if 'volume' in data.columns:
            ax2.bar(data.index, data['volume'], color=self.colors['volume'], alpha=0.6, width=0.8)
            ax2.set_title('Volume')
            ax2.set_ylabel('Volume')
            ax2.grid(True, alpha=0.3)
        
        # Equity curve
        ax3 = axes[2]
        if not trades.empty and 'cumulative_pnl' in trades.columns:
            equity_curve = trades['cumulative_pnl'].values
            ax3.plot(trades.index, equity_curve, color=self.colors['profit'], linewidth=2)
            ax3.fill_between(trades.index, equity_curve, alpha=0.3, color=self.colors['profit'])
            ax3.set_title('Equity Curve')
            ax3.set_ylabel('Cumulative PnL')
            ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def plot_optimization_results(self, study_results: List[Dict], 
                                title: str = "Optimization Results") -> plt.Figure:
        """
        Plot optimization results and parameter importance.
        
        Args:
            study_results: List of optimization trials
            title: Chart title
            
        Returns:
            matplotlib Figure
        """
        if not study_results:
            return self._create_empty_plot("No optimization results to display")
            
        df = pd.DataFrame(study_results)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(title, fontsize=16, fontweight='bold')
        
        # Optimization progress
        ax1 = axes[0, 0]
        if 'value' in df.columns:
            ax1.plot(df.index, df['value'], color=self.colors['price'], linewidth=2)
            ax1.set_title('Optimization Progress')
            ax1.set_xlabel('Trial')
            ax1.set_ylabel('Objective Value')
            ax1.grid(True, alpha=0.3)
        
        # Parameter distribution (assuming some common parameters)
        ax2 = axes[0, 1]
        param_cols = [col for col in df.columns if col.startswith('param_')]
        if param_cols:
            param_name = param_cols[0]
            ax2.hist(df[param_name].dropna(), bins=20, alpha=0.7, color=self.colors['signal'])
            ax2.set_title(f'{param_name} Distribution')
            ax2.set_xlabel(param_name)
            ax2.set_ylabel('Frequency')
            ax2.grid(True, alpha=0.3)
        
        # Score vs parameter scatter
        ax3 = axes[1, 0]
        if len(param_cols) >= 2 and 'value' in df.columns:
            scatter = ax3.scatter(df[param_cols[0]], df[param_cols[1]], 
                                c=df['value'], cmap='viridis', alpha=0.7)
            ax3.set_title(f'{param_cols[0]} vs {param_cols[1]}')
            ax3.set_xlabel(param_cols[0])
            ax3.set_ylabel(param_cols[1])
            plt.colorbar(scatter, ax=ax3, label='Score')
            ax3.grid(True, alpha=0.3)
        
        # Best trials
        ax4 = axes[1, 1]
        if 'value' in df.columns:
            top_trials = df.nlargest(10, 'value')
            ax4.barh(range(len(top_trials)), top_trials['value'], color=self.colors['profit'])
            ax4.set_title('Top 10 Trials')
            ax4.set_xlabel('Score')
            ax4.set_ylabel('Trial Rank')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def plot_walk_forward_analysis(self, wf_results: List[Dict], 
                                 title: str = "Walk-Forward Analysis") -> plt.Figure:
        """
        Plot walk-forward analysis results.
        
        Args:
            wf_results: Walk-forward analysis results
            title: Chart title
            
        Returns:
            matplotlib Figure
        """
        if not wf_results:
            return self._create_empty_plot("No walk-forward results to display")
            
        df = pd.DataFrame(wf_results)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(title, fontsize=16, fontweight='bold')
        
        # Period performance
        ax1 = axes[0, 0]
        if 'period' in df.columns and 'return' in df.columns:
            ax1.bar(df['period'], df['return'], 
                   color=[self.colors['profit'] if x > 0 else self.colors['loss'] for x in df['return']])
            ax1.set_title('Returns by Period')
            ax1.set_xlabel('Period')
            ax1.set_ylabel('Return %')
            ax1.grid(True, alpha=0.3)
            ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Cumulative performance
        ax2 = axes[0, 1]
        if 'return' in df.columns:
            cumulative = (1 + df['return'] / 100).cumprod()
            ax2.plot(df.index, cumulative, color=self.colors['price'], linewidth=2)
            ax2.set_title('Cumulative Performance')
            ax2.set_xlabel('Period')
            ax2.set_ylabel('Cumulative Return')
            ax2.grid(True, alpha=0.3)
        
        # Sharpe ratio evolution
        ax3 = axes[1, 0]
        if 'sharpe' in df.columns:
            ax3.plot(df.index, df['sharpe'], color=self.colors['signal'], linewidth=2, marker='o')
            ax3.set_title('Sharpe Ratio Evolution')
            ax3.set_xlabel('Period')
            ax3.set_ylabel('Sharpe Ratio')
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Drawdown analysis
        ax4 = axes[1, 1]
        if 'max_drawdown' in df.columns:
            ax4.fill_between(df.index, df['max_drawdown'], 0, 
                           color=self.colors['loss'], alpha=0.6)
            ax4.set_title('Maximum Drawdown')
            ax4.set_xlabel('Period')
            ax4.set_ylabel('Drawdown %')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def plot_risk_metrics(self, metrics: Dict[str, Any], 
                         title: str = "Risk Analysis") -> plt.Figure:
        """
        Plot comprehensive risk metrics.
        
        Args:
            metrics: Risk metrics dictionary
            title: Chart title
            
        Returns:
            matplotlib Figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(title, fontsize=16, fontweight='bold')
        
        # Risk-Return scatter
        ax1 = axes[0, 0]
        if 'annual_return' in metrics and 'annual_volatility' in metrics:
            ax1.scatter([metrics['annual_volatility']], [metrics['annual_return']], 
                       s=200, color=self.colors['price'], alpha=0.7)
            ax1.set_title('Risk-Return Profile')
            ax1.set_xlabel('Annual Volatility (%)')
            ax1.set_ylabel('Annual Return (%)')
            ax1.grid(True, alpha=0.3)
        
        # Drawdown timeline
        ax2 = axes[0, 1]
        if 'drawdown_series' in metrics:
            dd_series = metrics['drawdown_series']
            ax2.fill_between(range(len(dd_series)), dd_series, 0, 
                           color=self.colors['loss'], alpha=0.6)
            ax2.set_title('Drawdown Timeline')
            ax2.set_xlabel('Time')
            ax2.set_ylabel('Drawdown %')
            ax2.grid(True, alpha=0.3)
        
        # Key metrics radar (simplified)
        ax3 = axes[1, 0]
        key_metrics = ['sharpe_ratio', 'sortino_ratio', 'calmar_ratio']
        values = [metrics.get(metric, 0) for metric in key_metrics]
        if any(values):
            ax3.bar(key_metrics, values, color=[self.colors['profit'] if x > 0 else self.colors['loss'] for x in values])
            ax3.set_title('Key Risk Ratios')
            ax3.set_ylabel('Ratio Value')
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Win/Loss distribution
        ax4 = axes[1, 1]
        if 'win_rate' in metrics:
            win_rate = metrics['win_rate']
            loss_rate = 100 - win_rate
            ax4.pie([win_rate, loss_rate], labels=['Wins', 'Losses'], 
                   colors=[self.colors['profit'], self.colors['loss']], autopct='%1.1f%%')
            ax4.set_title(f'Win/Loss Distribution (Win Rate: {win_rate:.1f}%)')
        
        plt.tight_layout()
        return fig
    
    def create_interactive_performance_chart(self, data: pd.DataFrame, 
                                           trades: pd.DataFrame = None) -> go.Figure:
        """
        Create interactive Plotly chart for strategy performance.
        
        Args:
            data: Price data with indicators
            trades: Trade results
            
        Returns:
            Plotly Figure
        """
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxis=True,
            vertical_spacing=0.1,
            subplot_titles=('Price & Signals', 'Volume', 'Equity Curve'),
            row_heights=[0.5, 0.2, 0.3]
        )
        
        # Price chart
        fig.add_trace(
            go.Candlestick(
                x=data.index,
                open=data.get('open', data['close']),
                high=data.get('high', data['close']),
                low=data.get('low', data['close']),
                close=data['close'],
                name="Price"
            ),
            row=1, col=1
        )
        
        # Add technical indicators
        if 'ema_fast' in data.columns:
            fig.add_trace(
                go.Scatter(x=data.index, y=data['ema_fast'], name='EMA Fast', line=dict(width=1)),
                row=1, col=1
            )
        
        # Volume
        if 'volume' in data.columns:
            fig.add_trace(
                go.Bar(x=data.index, y=data['volume'], name='Volume', opacity=0.6),
                row=2, col=1
            )
        
        # Equity curve
        if trades is not None and not trades.empty and 'cumulative_pnl' in trades.columns:
            fig.add_trace(
                go.Scatter(x=trades.index, y=trades['cumulative_pnl'], 
                          name='Equity Curve', line=dict(width=2)),
                row=3, col=1
            )
        
        fig.update_layout(
            title="Interactive Strategy Performance",
            xaxis_rangeslider_visible=False,
            height=800
        )
        
        return fig
    
    def save_chart(self, fig: plt.Figure, filepath: str, dpi: int = 300) -> None:
        """
        Save matplotlib figure to file.
        
        Args:
            fig: Matplotlib figure
            filepath: Output file path
            dpi: Resolution
        """
        try:
            fig.savefig(filepath, dpi=dpi, bbox_inches='tight', facecolor='white')
            print(f"Chart saved to: {filepath}")
        except Exception as e:
            print(f"Error saving chart: {e}")
    
    def _create_empty_plot(self, message: str) -> plt.Figure:
        """Create empty plot with message."""
        fig, ax = plt.subplots(figsize=self.figsize)
        ax.text(0.5, 0.5, message, ha='center', va='center', transform=ax.transAxes, fontsize=14)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return fig 