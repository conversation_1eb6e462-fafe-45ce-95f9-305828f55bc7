"""
Risk management module for Ultratrader optimization system.

Provides comprehensive risk management including position sizing,
drawdown control, and portfolio protection mechanisms.
"""

from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from core.config import config


class RiskLevel(Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class RiskMetrics:
    """Container for risk metrics."""
    current_drawdown: float
    max_drawdown: float
    consecutive_losses: int
    daily_pnl: float
    portfolio_heat: float
    risk_level: RiskLevel


@dataclass
class PositionInfo:
    """Container for position information."""
    size: float
    entry_price: float
    stop_loss: float
    take_profit: Optional[float]
    risk_amount: float
    side: str


class PositionSizer:
    """
    Advanced position sizing with multiple methodologies.
    
    Features:
    - Fixed fractional sizing
    - Kelly criterion sizing
    - ATR-based sizing
    - Volatility-adjusted sizing
    - Risk parity sizing
    """
    
    @staticmethod
    def calculate_fixed_fractional_size(
        balance: float, 
        risk_per_trade: float, 
        entry_price: float, 
        stop_loss: float
    ) -> float:
        """
        Calculate position size using fixed fractional method.
        
        Args:
            balance: Current account balance
            risk_per_trade: Risk percentage per trade (e.g., 0.01 for 1%)
            entry_price: Entry price
            stop_loss: Stop loss price
            
        Returns:
            Position size in base currency
        """
        if entry_price <= 0 or stop_loss <= 0:
            return 0.0
        
        risk_amount = balance * risk_per_trade
        price_risk = abs(entry_price - stop_loss)
        
        if price_risk <= 0:
            return 0.0
        
        return risk_amount / price_risk
    
    @staticmethod
    def calculate_atr_based_size(
        balance: float, 
        risk_per_trade: float, 
        atr_value: float, 
        atr_multiplier: float = 2.0
    ) -> float:
        """
        Calculate position size based on ATR (Average True Range).
        
        Args:
            balance: Current account balance
            risk_per_trade: Risk percentage per trade
            atr_value: Current ATR value
            atr_multiplier: ATR multiplier for stop distance
            
        Returns:
            Position size in base currency
        """
        if atr_value <= 0:
            return 0.0
        
        risk_amount = balance * risk_per_trade
        stop_distance = atr_value * atr_multiplier
        
        return risk_amount / stop_distance
    
    @staticmethod
    def calculate_kelly_size(
        balance: float, 
        win_rate: float, 
        avg_win: float, 
        avg_loss: float,
        max_kelly_fraction: float = 0.25
    ) -> float:
        """
        Calculate position size using Kelly criterion.
        
        Args:
            balance: Current account balance
            win_rate: Historical win rate (0-1)
            avg_win: Average winning trade amount
            avg_loss: Average losing trade amount (positive value)
            max_kelly_fraction: Maximum Kelly fraction to use
            
        Returns:
            Recommended Kelly fraction
        """
        if avg_loss <= 0 or win_rate <= 0 or win_rate >= 1:
            return 0.0
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        
        # Cap the Kelly fraction to prevent excessive risk
        kelly_fraction = max(0, min(kelly_fraction, max_kelly_fraction))
        
        return kelly_fraction
    
    @staticmethod
    def calculate_volatility_adjusted_size(
        balance: float, 
        base_risk: float, 
        current_volatility: float, 
        avg_volatility: float
    ) -> float:
        """
        Adjust position size based on current market volatility.
        
        Args:
            balance: Current account balance
            base_risk: Base risk percentage
            current_volatility: Current market volatility
            avg_volatility: Average historical volatility
            
        Returns:
            Volatility-adjusted position size factor
        """
        if avg_volatility <= 0 or current_volatility <= 0:
            return base_risk
        
        # Reduce size when volatility is high, increase when low
        volatility_ratio = current_volatility / avg_volatility
        adjusted_risk = base_risk / volatility_ratio
        
        # Cap the adjustment
        min_risk = base_risk * 0.25  # Minimum 25% of base risk
        max_risk = base_risk * 2.0   # Maximum 200% of base risk
        
        return max(min_risk, min(adjusted_risk, max_risk))


class DrawdownController:
    """
    Advanced drawdown control and portfolio protection.
    
    Features:
    - Real-time drawdown monitoring
    - Dynamic risk adjustment
    - Circuit breaker mechanisms
    - Recovery protocols
    """
    
    def __init__(self):
        self.peak_balance = 0.0
        self.current_drawdown = 0.0
        self.max_historical_drawdown = 0.0
        self.consecutive_losses = 0
        self.risk_reduction_factor = 1.0
    
    def update_drawdown(self, current_balance: float) -> RiskMetrics:
        """
        Update drawdown calculations and risk metrics.
        
        Args:
            current_balance: Current account balance
            
        Returns:
            Updated risk metrics
        """
        # Update peak balance
        if current_balance > self.peak_balance:
            self.peak_balance = current_balance
            self.current_drawdown = 0.0
        else:
            # Calculate current drawdown
            self.current_drawdown = (self.peak_balance - current_balance) / self.peak_balance
        
        # Update max historical drawdown
        if self.current_drawdown > self.max_historical_drawdown:
            self.max_historical_drawdown = self.current_drawdown
        
        # Determine risk level
        risk_level = self._assess_risk_level(self.current_drawdown)
        
        # Calculate portfolio heat (placeholder - would need open positions)
        portfolio_heat = self.current_drawdown  # Simplified
        
        return RiskMetrics(
            current_drawdown=self.current_drawdown,
            max_drawdown=self.max_historical_drawdown,
            consecutive_losses=self.consecutive_losses,
            daily_pnl=0.0,  # Would be calculated elsewhere
            portfolio_heat=portfolio_heat,
            risk_level=risk_level
        )
    
    def _assess_risk_level(self, drawdown: float) -> RiskLevel:
        """Assess current risk level based on drawdown."""
        if drawdown < 0.05:  # Less than 5%
            return RiskLevel.LOW
        elif drawdown < 0.10:  # 5-10%
            return RiskLevel.MEDIUM
        elif drawdown < 0.15:  # 10-15%
            return RiskLevel.HIGH
        else:  # Above 15%
            return RiskLevel.EXTREME
    
    def should_stop_trading(self, risk_metrics: RiskMetrics) -> bool:
        """
        Determine if trading should be stopped based on risk metrics.
        
        Args:
            risk_metrics: Current risk metrics
            
        Returns:
            True if trading should be stopped
        """
        # Stop if max drawdown exceeded
        if risk_metrics.current_drawdown >= config.trading.max_drawdown_limit:
            return True
        
        # Stop if too many consecutive losses
        if risk_metrics.consecutive_losses >= config.trading.max_consecutive_losses:
            return True
        
        # Stop if daily loss limit exceeded
        if risk_metrics.daily_pnl <= -config.trading.daily_loss_limit:
            return True
        
        return False
    
    def calculate_risk_adjustment_factor(self, risk_metrics: RiskMetrics) -> float:
        """
        Calculate risk adjustment factor based on current conditions.
        
        Args:
            risk_metrics: Current risk metrics
            
        Returns:
            Risk adjustment factor (0.0 to 1.0)
        """
        base_factor = 1.0
        
        # Adjust based on drawdown
        if risk_metrics.current_drawdown > 0.05:
            base_factor *= (1.0 - risk_metrics.current_drawdown)
        
        # Adjust based on consecutive losses
        if risk_metrics.consecutive_losses >= 2:
            base_factor *= 0.5  # Reduce risk by 50%
        
        # Adjust based on risk level
        if risk_metrics.risk_level == RiskLevel.HIGH:
            base_factor *= 0.3
        elif risk_metrics.risk_level == RiskLevel.EXTREME:
            base_factor *= 0.1
        
        return max(0.1, base_factor)  # Minimum 10% of normal risk


class RiskManager:
    """
    Main risk management system integrating all risk components.
    
    Features:
    - Integrated position sizing
    - Real-time risk monitoring
    - Dynamic risk adjustment
    - Portfolio protection
    """
    
    def __init__(self):
        self.position_sizer = PositionSizer()
        self.drawdown_controller = DrawdownController()
        self.daily_trades = 0
        self.daily_loss_count = 0
        self.last_trade_time = None
    
    def calculate_position_size(
        self,
        balance: float,
        entry_price: float,
        stop_loss: float,
        atr_value: float,
        market_volatility: float = None,
        historical_metrics: Dict[str, float] = None
    ) -> float:
        """
        Calculate optimal position size using multiple methodologies.
        
        Args:
            balance: Current account balance
            entry_price: Planned entry price
            stop_loss: Planned stop loss price
            atr_value: Current ATR value
            market_volatility: Current market volatility
            historical_metrics: Historical performance metrics
            
        Returns:
            Recommended position size
        """
        # Get current risk metrics
        risk_metrics = self.drawdown_controller.update_drawdown(balance)
        
        # Check if trading should be stopped
        if self.drawdown_controller.should_stop_trading(risk_metrics):
            return 0.0
        
        # Calculate base risk per trade
        base_risk = config.trading.risk_per_trade
        
        # Apply risk adjustment factor
        risk_adjustment = self.drawdown_controller.calculate_risk_adjustment_factor(risk_metrics)
        adjusted_risk = base_risk * risk_adjustment
        
        # Calculate position size using primary method (fixed fractional)
        primary_size = self.position_sizer.calculate_fixed_fractional_size(
            balance, adjusted_risk, entry_price, stop_loss
        )
        
        # Calculate ATR-based size as secondary validation
        atr_size = self.position_sizer.calculate_atr_based_size(
            balance, adjusted_risk, atr_value
        )
        
        # Use the more conservative size
        final_size = min(primary_size, atr_size) if atr_size > 0 else primary_size
        
        # Apply position size limits
        max_position_value = balance * config.trading.max_position_size_pct
        max_size = max_position_value / entry_price
        final_size = min(final_size, max_size)
        
        # Apply volatility adjustment if available
        if market_volatility is not None:
            avg_volatility = 0.02  # Default 2% daily volatility
            volatility_factor = self.position_sizer.calculate_volatility_adjusted_size(
                balance, 1.0, market_volatility, avg_volatility
            )
            final_size *= volatility_factor
        
        return max(0.0, final_size)
    
    def validate_trade_setup(
        self,
        balance: float,
        entry_price: float,
        stop_loss: float,
        take_profit: Optional[float] = None
    ) -> Tuple[bool, str]:
        """
        Validate if a trade setup meets risk criteria.
        
        Args:
            balance: Current account balance
            entry_price: Planned entry price
            stop_loss: Planned stop loss price
            take_profit: Optional take profit price
            
        Returns:
            Tuple of (is_valid, reason)
        """
        # Basic validation
        if entry_price <= 0 or stop_loss <= 0:
            return False, "Invalid price levels"
        
        if balance <= 0:
            return False, "Insufficient balance"
        
        # Risk-reward validation
        if take_profit is not None:
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            if risk_reward_ratio < 1.0:  # Minimum 1:1 risk-reward
                return False, f"Poor risk-reward ratio: {risk_reward_ratio:.2f}"
        
        # Risk percentage validation
        risk_amount = abs(entry_price - stop_loss) / entry_price
        if risk_amount > 0.1:  # More than 10% risk per trade
            return False, f"Excessive risk per trade: {risk_amount*100:.1f}%"
        
        # Check if trading is allowed
        risk_metrics = self.drawdown_controller.update_drawdown(balance)
        if self.drawdown_controller.should_stop_trading(risk_metrics):
            return False, f"Trading stopped due to {risk_metrics.risk_level.value} risk level"
        
        return True, "Trade setup validated"
    
    def update_trade_result(self, pnl: float, is_winner: bool):
        """
        Update risk manager with trade results.
        
        Args:
            pnl: Profit/loss from the trade
            is_winner: Whether the trade was profitable
        """
        if not is_winner:
            self.drawdown_controller.consecutive_losses += 1
            self.daily_loss_count += 1
        else:
            self.drawdown_controller.consecutive_losses = 0
        
        self.daily_trades += 1
    
    def get_current_risk_status(self, balance: float) -> Dict[str, Any]:
        """
        Get comprehensive risk status report.
        
        Args:
            balance: Current account balance
            
        Returns:
            Dictionary with risk status information
        """
        risk_metrics = self.drawdown_controller.update_drawdown(balance)
        risk_adjustment = self.drawdown_controller.calculate_risk_adjustment_factor(risk_metrics)
        
        return {
            'current_drawdown': risk_metrics.current_drawdown,
            'max_drawdown': risk_metrics.max_drawdown,
            'consecutive_losses': risk_metrics.consecutive_losses,
            'risk_level': risk_metrics.risk_level.value,
            'risk_adjustment_factor': risk_adjustment,
            'trading_allowed': not self.drawdown_controller.should_stop_trading(risk_metrics),
            'daily_trades': self.daily_trades,
            'daily_losses': self.daily_loss_count
        }
    
    def reset_daily_counters(self):
        """Reset daily trading counters."""
        self.daily_trades = 0
        self.daily_loss_count = 0 