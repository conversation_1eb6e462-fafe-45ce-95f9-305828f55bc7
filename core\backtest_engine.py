"""
Backtest engine for Ultratrader optimization system.

High-performance backtesting engine with realistic order execution,
transaction costs, and comprehensive performance metrics.
"""

import numpy as np
import pandas as pd
from numba import jit
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from datetime import datetime

from .config import config
from risk.risk_manager import RiskManager


@dataclass
class TradeResult:
    """Container for individual trade results."""
    entry_time: datetime
    exit_time: datetime
    entry_price: float
    exit_price: float
    size: float
    side: str  # 'LONG' or 'SHORT'
    pnl: float
    fees: float
    duration_bars: int
    exit_reason: str  # 'stop_loss', 'take_profit', 'trailing_stop', 'strategy_exit'


@dataclass
class BacktestResults:
    """Container for backtest results."""
    trades: List[TradeResult]
    equity_curve: np.ndarray
    timestamps: List[datetime]
    
    # Performance metrics
    total_return: float
    sortino_ratio: float
    profit_factor: float
    max_drawdown: float
    win_rate: float
    avg_win: float
    avg_loss: float
    trade_count: int
    
    # Risk metrics
    var_95: float  # Value at Risk at 95% confidence
    expected_shortfall: float
    
    # Additional metrics
    sharpe_ratio: float
    calmar_ratio: float
    recovery_factor: float


@jit(nopython=True, cache=True, fastmath=True)
def calculate_trade_metrics_numba(equity_curve: np.ndarray, initial_balance: float) -> Tuple[float, float, float, float]:
    """
    Optimized metrics calculation using Numba for maximum performance.

    Args:
        equity_curve: Array of equity values over time
        initial_balance: Starting balance

    Returns:
        Tuple of (sortino_ratio, profit_factor, max_drawdown, final_balance)
    """
    if len(equity_curve) == 0:
        return -999.0, 0.0, 1.0, initial_balance

    # Calculate returns more efficiently
    returns = np.empty(len(equity_curve) - 1)
    for i in range(1, len(equity_curve)):
        if equity_curve[i-1] != 0:
            returns[i-1] = (equity_curve[i] - equity_curve[i-1]) / equity_curve[i-1]
        else:
            returns[i-1] = 0.0

    if len(returns) == 0:
        return -999.0, 0.0, 1.0, equity_curve[-1]
    
    # Sortino ratio calculation
    downside_returns = returns[returns < 0]
    if len(downside_returns) == 0:
        sortino = 9999.0 if np.mean(returns) > 0 else 0.0
    else:
        downside_std = np.std(downside_returns)
        if downside_std > 0:
            # Corrected annualizer for 15min bars: periods per day * sqrt(trading days per year)
            periods_per_day = 96  # 24 hours * 4 (15min periods per hour)
            annualizer = np.sqrt(periods_per_day * 252)  # 252 trading days ≈ 158
            sortino = (np.mean(returns) / downside_std) * annualizer
            sortino = max(min(sortino, 9999.0), -9999.0)  # Cap both positive and negative extremes
        else:
            sortino = 0.0
    
    # Profit factor calculation
    gross_gains = np.sum(returns[returns > 0])
    gross_losses = abs(np.sum(returns[returns < 0]))
    profit_factor = gross_gains / gross_losses if gross_losses > 0 else 100.0
    
    # Maximum drawdown calculation
    peak = equity_curve[0]
    max_dd = 0.0
    for value in equity_curve:
        if value > peak:
            peak = value
        dd = (peak - value) / peak if peak > 0 else 0.0
        if dd > max_dd:
            max_dd = dd
    
    return sortino, profit_factor, max_dd, equity_curve[-1]


@jit(nopython=True, cache=True, fastmath=True)
def calculate_signals_numba(
    close_prices: np.ndarray,
    ema_fast: np.ndarray,
    ema_slow: np.ndarray,
    rsi: np.ndarray,
    atr: np.ndarray,
    volume: np.ndarray,
    lookback: int = 20
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Optimized signal calculation using Numba for maximum performance.

    Returns:
        Tuple of (long_signals, short_signals) as boolean arrays
    """
    n = len(close_prices)
    long_signals = np.zeros(n, dtype=np.bool_)
    short_signals = np.zeros(n, dtype=np.bool_)

    for i in range(lookback, n):
        # Trend condition
        trend_up = ema_fast[i] > ema_slow[i]
        trend_down = ema_fast[i] < ema_slow[i]

        # RSI conditions
        rsi_oversold = rsi[i] < 30
        rsi_overbought = rsi[i] > 70

        # Volume condition (simple above average)
        avg_volume = 0.0
        for j in range(i - lookback, i):
            avg_volume += volume[j]
        avg_volume /= lookback
        volume_spike = volume[i] > avg_volume * 1.2

        # Long signal
        if trend_up and rsi_oversold and volume_spike:
            long_signals[i] = True

        # Short signal
        if trend_down and rsi_overbought and volume_spike:
            short_signals[i] = True

    return long_signals, short_signals


class BacktestEngine:
    """
    High-performance backtesting engine with realistic execution simulation.
    
    Features:
    - Realistic order execution with slippage
    - Transaction cost modeling
    - Risk management integration
    - Multiple strategy modes
    - Comprehensive performance metrics
    """
    
    def __init__(self, risk_manager: Optional[RiskManager] = None):
        self.risk_manager = risk_manager or RiskManager()
        self.trades: List[TradeResult] = []
        self.equity_curve: List[float] = []
        self.timestamps: List[datetime] = []
    
    def run_backtest(
        self,
        df: pd.DataFrame,
        params: Dict[str, Any],
        strategy_mode: str = 'Adaptive',
        initial_balance: float = None
    ) -> BacktestResults:
        """
        Run comprehensive backtest with realistic execution.
        
        Args:
            df: DataFrame with OHLCV data and indicators
            params: Strategy parameters
            strategy_mode: Strategy mode ('Trend', 'Reversion', 'Adaptive')
            initial_balance: Starting balance
            
        Returns:
            BacktestResults object with all metrics
        """
        if df.empty or len(df) < 50:
            return self._create_empty_results(initial_balance or config.trading.initial_balance)
        
        balance = initial_balance or config.trading.initial_balance
        self.trades.clear()
        self.equity_curve = [balance]
        self.timestamps = [df.index[0]]
        
        # Run the actual backtest
        results = self._execute_backtest_loop(df, params, strategy_mode, balance)
        
        # Calculate comprehensive metrics
        results = self._calculate_comprehensive_metrics(results, balance)
        
        # COMPACT BACKTEST SUMMARY - Only show for significant results
        if results.trade_count > 0 and abs(results.total_return) > 0.01:  # Only show if >1% return
            print(f"📊 {strategy_mode}: {results.total_return*100:.1f}% return, "
                  f"{results.trade_count} trades, {results.win_rate*100:.0f}% win rate")
        
        return results
    
    def _execute_backtest_loop(
        self,
        df: pd.DataFrame,
        params: Dict[str, Any],
        strategy_mode: str,
        initial_balance: float
    ) -> Dict[str, Any]:
        """Execute the main backtest loop."""
        
        # Initialize state
        realized_equity = initial_balance
        position = {}
        
        # Risk tracking
        consecutive_losses = 0
        last_trade_bar = -999
        daily_pnl = 0
        daily_start_equity = initial_balance
        losing_trades = 0
        winning_trades = 0
        
        # Pre-calculate indicator arrays for performance
        arrays = self._prepare_indicator_arrays(df, params)
        
        # Main backtest loop
        for i in range(1, len(df)):
            current_time = df.index[i]
            price = arrays['close_prices'][i]
            high = arrays['high_prices'][i]
            low = arrays['low_prices'][i]
            atr = arrays['atr_values'][i]
            
            # Process position exits first
            if position:
                exit_result = self._process_position_exit(
                    position, i, price, high, low, atr, params, current_time
                )
                
                if exit_result:
                    trade, net_pnl = exit_result
                    self.trades.append(trade)
                    realized_equity += net_pnl
                    daily_pnl += net_pnl
                    
                    # Update risk tracking
                    if net_pnl > 0:
                        winning_trades += 1
                        consecutive_losses = 0
                    else:
                        losing_trades += 1
                        consecutive_losses += 1
                    
                    position = {}
            
            # Process new entries
            if not position and atr > 0:
                entry_signal = self._generate_signals(
                    arrays, i, params, current_time, strategy_mode
                )
                
                if entry_signal:
                    side, entry_price = entry_signal
                    position = self._create_position(
                        side, entry_price, atr, params, realized_equity, current_time, i
                    )
                    last_trade_bar = i
            
            # Update equity curve
            mark_to_market_equity = self._calculate_mark_to_market(
                realized_equity, position, price
            )
            self.equity_curve.append(mark_to_market_equity)
            self.timestamps.append(current_time)
        
        return {
            'final_balance': realized_equity,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'consecutive_losses': consecutive_losses
        }
    
    def _prepare_indicator_arrays(self, df: pd.DataFrame, params: Dict[str, Any]) -> Dict[str, np.ndarray]:
        """Pre-calculate indicator arrays for performance."""
        arrays = {
            'close_prices': df['close'].to_numpy(),
            'high_prices': df['high'].to_numpy(),
            'low_prices': df['low'].to_numpy(),
            'volume_values': df['volume'].to_numpy(),
        }
        
        # ATR - first try the 'atr' column, then try the formatted column name
        if 'atr' in df.columns:
            arrays['atr_values'] = df['atr'].to_numpy()
        else:
            atr_col = f"ATR_{params.get('atr_len', 14)}"
            arrays['atr_values'] = df[atr_col].to_numpy() if atr_col in df.columns else np.full(len(df), 0.01)
        
        # EMAs
        ema_fast_col = f"EMA_{params.get('ema_fast', 12)}"
        ema_slow_col = f"EMA_{params.get('ema_slow', 26)}"
        arrays['ema_fast_values'] = df[ema_fast_col].to_numpy() if ema_fast_col in df.columns else np.full(len(df), np.nan)
        arrays['ema_slow_values'] = df[ema_slow_col].to_numpy() if ema_slow_col in df.columns else np.full(len(df), np.nan)
        
        # Other indicators
        adx_col = f"ADX_{params.get('adx_len', 14)}"
        arrays['adx_values'] = df[adx_col].to_numpy() if adx_col in df.columns else np.full(len(df), 25)
        
        arrays['supertrend_dir_values'] = df['supertrend_dir'].to_numpy() if 'supertrend_dir' in df.columns else np.full(len(df), 0)
        arrays['mta_trend_values'] = df['mta_trend'].to_numpy() if 'mta_trend' in df.columns else np.full(len(df), 1)
        arrays['volume_sma_values'] = df['volume_sma'].to_numpy() if 'volume_sma' in df.columns else np.full(len(df), np.nan)
        
        # Bollinger Bands
        arrays['bb_low_values'] = df['bb_low'].to_numpy() if 'bb_low' in df.columns else np.full(len(df), np.nan)
        arrays['bb_high_values'] = df['bb_high'].to_numpy() if 'bb_high' in df.columns else np.full(len(df), np.nan)
        arrays['bb_position_values'] = df['bb_position'].to_numpy() if 'bb_position' in df.columns else np.full(len(df), 0.5)
        
        # Momentum indicators
        rsi_col = f"RSI_{params.get('rsi_len', 14)}"
        arrays['rsi_values'] = df[rsi_col].to_numpy() if rsi_col in df.columns else np.full(len(df), 50)
        
        arrays['macd_line_values'] = df['macd_line'].to_numpy() if 'macd_line' in df.columns else np.full(len(df), 0)
        arrays['macd_signal_values'] = df['macd_signal'].to_numpy() if 'macd_signal' in df.columns else np.full(len(df), 0)
        
        # Market condition indicators
        arrays['trend_strength_values'] = df['trend_strength'].to_numpy() if 'trend_strength' in df.columns else np.full(len(df), 0.5)
        arrays['volatility_percentile_values'] = df['volatility_percentile'].to_numpy() if 'volatility_percentile' in df.columns else np.full(len(df), 50)
        
        return arrays
    
    def _process_position_exit(
        self,
        position: Dict[str, Any],
        bar_index: int,
        price: float,
        high: float,
        low: float,
        atr: float,
        params: Dict[str, Any],
        current_time: datetime
    ) -> Optional[Tuple[TradeResult, float]]:
        """Process position exit logic."""
        exit_triggered = False
        exit_price = price
        exit_reason = "strategy_exit"
        
        if position['side'] == 'LONG':
            # Update trailing stop
            position['trailing_stop'] = max(
                position['trailing_stop'], 
                price - params['trail_atr_mult'] * atr
            )
            
            # Check exits (stops first, then take profit)
            if low <= position['stop_loss']:
                exit_triggered = True
                exit_price = position['stop_loss']
                exit_reason = "stop_loss"
            elif low <= position['trailing_stop']:
                exit_triggered = True
                exit_price = position['trailing_stop']
                exit_reason = "trailing_stop"
            elif 'take_profit' in position and high >= position['take_profit']:
                exit_triggered = True
                exit_price = position['take_profit']
                exit_reason = "take_profit"
                
            if exit_triggered:
                gross_pnl = (exit_price - position['entry_price']) * position['qty']
                
        elif position['side'] == 'SHORT':
            # Update trailing stop
            position['trailing_stop'] = min(
                position['trailing_stop'], 
                price + params['trail_atr_mult'] * atr
            )
            
            # Check exits
            if high >= position['stop_loss']:
                exit_triggered = True
                exit_price = position['stop_loss']
                exit_reason = "stop_loss"
            elif high >= position['trailing_stop']:
                exit_triggered = True
                exit_price = position['trailing_stop']
                exit_reason = "trailing_stop"
            elif 'take_profit' in position and low <= position['take_profit']:
                exit_triggered = True
                exit_price = position['take_profit']
                exit_reason = "take_profit"
                
            if exit_triggered:
                gross_pnl = (position['entry_price'] - exit_price) * position['qty']
        
        if exit_triggered:
            # Calculate fees
            entry_cost = position['entry_price'] * position['qty'] * config.trading.futures_taker_fee
            exit_cost = exit_price * position['qty'] * config.trading.futures_taker_fee
            total_fees = entry_cost + exit_cost
            net_pnl = gross_pnl - total_fees
            
            # Create trade result
            trade = TradeResult(
                entry_time=position['entry_time'],
                exit_time=current_time,
                entry_price=position['entry_price'],
                exit_price=exit_price,
                size=position['qty'],
                side=position['side'],
                pnl=net_pnl,
                fees=total_fees,
                duration_bars=bar_index - position['entry_bar'],
                exit_reason=exit_reason
            )
            
            return trade, net_pnl
        
        return None
    
    def _generate_signals(
        self,
        arrays: Dict[str, np.ndarray],
        i: int,
        params: Dict[str, Any],
        current_time: datetime,
        strategy_mode: str = 'Adaptive'
    ) -> Optional[Tuple[str, float]]:
        """Generate trading signals - OPTIMIZED FOR MORE TRADES."""

        if i < 20:  # Reduced history requirement for more opportunities
            return None

        # Simple risk management checks
        risk_multiplier = params.get('risk_multiplier', 1.0)
        base_risk = config.trading.risk_per_trade * risk_multiplier

        if base_risk <= 0:
            return None

        # Basic position control - always allow both directions
        can_long = True
        can_short = True

        # RELAXED Volume confirmation - make it optional
        volume_filter_passed = self._check_volume_filter(arrays, i, params)
        # If volume filter fails, still allow trades 50% of the time
        if not volume_filter_passed:
            volume_filter_passed = (i % 2 == 0)  # Allow every other bar even without volume

        # Strategy mode decision - simplified for more trades
        is_trending = False
        if strategy_mode == 'Trend':
            is_trending = True
        elif strategy_mode == 'Reversion':
            is_trending = False
        elif strategy_mode == 'Adaptive':
            # More lenient ADX threshold for adaptive mode
            adx_trending = arrays['adx_values'][i] > params.get('adx_threshold', 15)  # Lower default
            is_trending = adx_trending

        price = arrays['close_prices'][i]

        # Entry signal logic based on strategy type
        if is_trending:
            return self._check_trend_signals(arrays, i, params, can_long, can_short, price, volume_filter_passed)
        else:
            return self._check_reversion_signals(arrays, i, params, can_long, can_short, price, volume_filter_passed)
    
    def _check_volume_filter(self, arrays: Dict[str, np.ndarray], i: int, params: Dict[str, Any]) -> bool:
        """Check volume filter conditions - MUCH MORE LENIENT."""
        # Make volume filter much more permissive
        if not params.get('use_volume_filter', False):
            return True

        if np.isnan(arrays['volume_sma_values'][i]):
            return True

        current_volume = arrays['volume_values'][i]
        avg_volume = arrays['volume_sma_values'][i]

        # Much more lenient volume requirement - only 5% above average
        volume_threshold = params.get('volume_threshold', 1.05)  # Reduced from 1.2 to 1.05
        return current_volume > avg_volume * volume_threshold
    
    def _check_trend_signals(
        self,
        arrays: Dict[str, np.ndarray],
        i: int,
        params: Dict[str, Any],
        can_long: bool,
        can_short: bool,
        price: float,
        volume_filter_passed: bool
    ) -> Optional[Tuple[str, float]]:
        """Check trend-following signals - MUCH MORE AGGRESSIVE FOR MORE TRADES."""

        # SIMPLIFIED trend confirmations - only need ONE confirmation
        ema_bullish = arrays['ema_fast_values'][i] > arrays['ema_slow_values'][i]
        supertrend_bullish = arrays['supertrend_dir_values'][i] == 1
        adx_trending = arrays['adx_values'][i] > params.get('adx_threshold', 15)  # Lower threshold

        # Volume confirmation - much more lenient
        volume_ok = volume_filter_passed or not params.get('use_volume_filter', True)

        # MUCH MORE AGGRESSIVE Long signal - only need EMA OR Supertrend
        if can_long and (ema_bullish or supertrend_bullish):
            return 'LONG', price

        # MUCH MORE AGGRESSIVE Short signal - only need EMA OR Supertrend
        if can_short and (not ema_bullish or not supertrend_bullish):
            return 'SHORT', price

        return None
    
    def _check_reversion_signals(
        self,
        arrays: Dict[str, np.ndarray],
        i: int,
        params: Dict[str, Any],
        can_long: bool,
        can_short: bool,
        price: float,
        volume_filter_passed: bool
    ) -> Optional[Tuple[str, float]]:
        """Check mean reversion signals with proper confirmations."""
        
        # Multiple mean reversion confirmations for better quality
        bb_oversold = arrays['bb_position_values'][i] < params.get('bb_oversold', 0.2)
        bb_overbought = arrays['bb_position_values'][i] > params.get('bb_overbought', 0.8)
        
        rsi_oversold = arrays['rsi_values'][i] < params.get('rsi_oversold', 30)
        rsi_overbought = arrays['rsi_values'][i] > params.get('rsi_overbought', 70)
        
        # Volume confirmation
        volume_ok = volume_filter_passed
        
        # Long signal (buy oversold) - require BB OR RSI confirmation (not both)
        if can_long and (bb_oversold or rsi_oversold):
            if volume_ok or not params.get('use_volume_filter', False):
                return 'LONG', price
        
        # Short signal (sell overbought) - require BB OR RSI confirmation (not both)
        if can_short and (bb_overbought or rsi_overbought):
            if volume_ok or not params.get('use_volume_filter', False):
                return 'SHORT', price
        
        return None
    
    def _create_position(
        self,
        side: str,
        entry_price: float,
        atr: float,
        params: Dict[str, Any],
        balance: float,
        entry_time: datetime,
        entry_bar: int
    ) -> Dict[str, Any]:
        """Create a new position."""
        # Calculate position size
        stop_loss_distance = atr * params['sl_atr_mult']
        
        if side == 'LONG':
            stop_loss = entry_price - stop_loss_distance
        else:
            stop_loss = entry_price + stop_loss_distance
        
        qty = self.risk_manager.calculate_position_size(
            balance, entry_price, stop_loss, atr
        )
        
        # Apply position size limits
        max_position_value = balance * config.trading.max_position_size_pct
        max_qty = max_position_value / entry_price
        qty = min(qty, max_qty)
        
        position = {
            'side': side,
            'entry_price': entry_price,
            'qty': qty,
            'stop_loss': stop_loss,
            'trailing_stop': stop_loss,
            'entry_time': entry_time,
            'entry_bar': entry_bar
        }
        
        # Add take profit if specified
        if 'tp_atr_mult' in params:
            if side == 'LONG':
                position['take_profit'] = entry_price + (atr * params['tp_atr_mult'])
            else:
                position['take_profit'] = entry_price - (atr * params['tp_atr_mult'])
        
        return position
    
    def _calculate_mark_to_market(
        self,
        realized_equity: float,
        position: Dict[str, Any],
        current_price: float
    ) -> float:
        """Calculate mark-to-market equity including unrealized PnL."""
        if not position:
            return realized_equity
        
        unrealized_pnl = 0.0
        if position['side'] == 'LONG':
            unrealized_pnl = (current_price - position['entry_price']) * position['qty']
        else:
            unrealized_pnl = (position['entry_price'] - current_price) * position['qty']
        
        return realized_equity + unrealized_pnl
    
    def _calculate_comprehensive_metrics(
        self,
        results: Dict[str, Any],
        initial_balance: float
    ) -> BacktestResults:
        """Calculate comprehensive performance metrics."""
        
        equity_array = np.array(self.equity_curve)
        
        # Calculate basic metrics using numba
        sortino, profit_factor, max_drawdown, final_balance = calculate_trade_metrics_numba(
            equity_array, initial_balance
        )
        
        # Calculate additional metrics
        total_return = (final_balance - initial_balance) / initial_balance
        
        # Trade-based metrics
        if self.trades:
            pnls = [trade.pnl for trade in self.trades]
            winning_trades = [trade for trade in self.trades if trade.pnl > 0]
            losing_trades = [trade for trade in self.trades if trade.pnl <= 0]
            
            win_rate = len(winning_trades) / len(self.trades)
            avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0.0
            avg_loss = np.mean([abs(t.pnl) for t in losing_trades]) if losing_trades else 0.0
            
            # Risk metrics
            returns = np.diff(equity_array) / equity_array[:-1]
            var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0.0
            expected_shortfall = np.mean(returns[returns <= var_95]) if len(returns) > 0 else 0.0
            
            # Additional ratios
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(365*24*4) if len(returns) > 1 else 0.0
            calmar_ratio = total_return / max_drawdown if max_drawdown > 0 else 0.0
            recovery_factor = total_return / max_drawdown if max_drawdown > 0 else 0.0
        else:
            win_rate = 0.0
            avg_win = 0.0
            avg_loss = 0.0
            var_95 = 0.0
            expected_shortfall = 0.0
            sharpe_ratio = 0.0
            calmar_ratio = 0.0
            recovery_factor = 0.0
        
        return BacktestResults(
            trades=self.trades.copy(),
            equity_curve=equity_array,
            timestamps=self.timestamps.copy(),
            total_return=total_return,
            sortino_ratio=sortino,
            profit_factor=profit_factor,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            trade_count=len(self.trades),
            var_95=var_95,
            expected_shortfall=expected_shortfall,
            sharpe_ratio=sharpe_ratio,
            calmar_ratio=calmar_ratio,
            recovery_factor=recovery_factor
        )
    
    def _create_empty_results(self, initial_balance: float) -> BacktestResults:
        """Create empty results for invalid backtests."""
        return BacktestResults(
            trades=[],
            equity_curve=np.array([initial_balance]),
            timestamps=[],
            total_return=0.0,
            sortino_ratio=-999.0,
            profit_factor=0.0,
            max_drawdown=0.0,
            win_rate=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            trade_count=0,
            var_95=0.0,
            expected_shortfall=0.0,
            sharpe_ratio=0.0,
            calmar_ratio=0.0,
            recovery_factor=0.0
        ) 